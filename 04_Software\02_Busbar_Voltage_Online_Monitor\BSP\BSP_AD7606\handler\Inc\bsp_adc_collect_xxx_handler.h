/******************************************************************************
 * @file bsp_adc_collect_xxx_handler.h
 * @brief 
 * <AUTHOR>
 * @version 1.0
 * @date 2024-10-16
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow:
 * 
 * @par dependencies
 * 
 * @note 1 tab == 4 spaces!
 * 
 *****************************************************************************/

#ifndef __BSP_ADC_COLLECT_XXX_HANDLER_H__
#define __BSP_ADC_COLLECT_XXX_HANDLER_H__

//******************************** Includes *********************************//
#include <stdint.h>
#include "ad7606_driver.h" 
#include "system_config.h"

//******************************** Includes *********************************//

//******************************** Defines **********************************//
#define ADC_COLLECT_HANDLER_RTOS_SUPPORTING
#define OS_TASK_PRIORITYNORMAL 28
#define VALID_ADC_CHANNEL_NUM  4
#ifdef ADC_COLLECT_HANDLER_RTOS_SUPPORTING

#define OS_TASK_PRIORITYMAX 56


#ifdef ADC_COLLECT_DEBUG
#define ADC_COLLECT_USE_INTERRUPT
#define ADC_COLLECT_LOG_IRQ
#endif /*  ADC_COLLECT_DEBUG*/



#ifdef ADC_COLLECT_DEBUG

#define ADC_COLLECT_DEBUG_PR(...) printf(__VA_ARGS__)

#ifdef ADC_COLLECT_LOG_D
#define ADC_COLLECT_LOG_DEUBG(...) log_d(__VA_ARGS__)
#else
#define ADC_COLLECT_LOG_DEUBG(...)
#endif /* ADC_COLLECT_LOG_D */

#define ADC_COLLECT_LOG_ERROR(...) log_e(__VA_ARGS__)


#ifdef ADC_COLLECT_LOG_IRQ
#define ADC_COLLECT_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
#else
#define ADC_COLLECT_LOG_IRQ(...)
#endif  /* ADC_COLLECT_LOG_I */

#else
#define ADC_COLLECT_DEBUG_PR(...)
#define ADC_COLLECT_LOG_DEUBG(...)
#define ADC_COLLECT_LOG_ERROR(...)
#define ADC_COLLECT_LOG_IRQ(...)
#endif /* ADC_COLLECT_DEBUG */

#define ADC_START_CHANNEL 0
#define ADC_USE_CHANNELS  4

typedef enum 
{
    ADC_COLLECT_RTOS_PDFALSE = 0, // 返回值为假
    ADC_COLLECT_RTOS_PDTRUE  = 1, // 返回值为真
}adc_collect_handler_os_ret_code_t;
#endif //END OF AD7606_RTOS_SUPPORTING

//adc采集句柄事件类型
typedef enum 
{
#ifdef ADC_COLLECT_USE_INTERRUPT
    ADC_COLLECT_CLEAR_DATA_EVENT = 0,
#else
    ADC_COLLECT_DATA_READ_EVENT   = 0,
#endif //END OF ADC_COLLECT_USE_INTERRUPT
    ADC_COLLECT_SET_RANGE         = 1,
    ADC_COLLECT_SET_OVER_SAMPLING = 2,
}adc_collect_xxx_event_data_t;

//adc采集范围
typedef enum
{
    ADC_COLLECT_RANGE_5V  = 0,
    ADC_COLLECT_RANGE_10V = 1,
}adc_collect_set_range_t;


typedef enum 
{
    // 无采样
    OVER_SAMPLING_NONE = 0,
    // 2倍采样
    OVER_SAMPLING_2    = 1,
    // 4倍采样
    OVER_SAMPLING_4    = 2,
    // 8倍采样
    OVER_SAMPLING_8    = 3,
    // 16倍采样
    OVER_SAMPLING_16   = 4,
    // 32倍采样
    OVER_SAMPLING_32   = 5,
    // 64倍采样
    OVER_SAMPLING_64   = 6,
}adc_collect_set_ovrsampling_t;
//adc采集采样率
typedef enum
{
    ADC_COLLECT_RATE_2K   = 0,
    ADC_COLLECT_RATE_6K   = 1,
    ADC_COLLECT_RATE_12K  = 2,
    ADC_COLLECT_RATE_25K  = 3,
    ADC_COLLECT_RATE_50K  = 4,
    ADC_COLLECT_RATE_100K = 5,
    ADC_COLLECT_RATE_200K = 6,
}adc_collect_rate_t;

typedef enum
{
    ADC_COLLCET_OK             = 0,
    ADC_COLLCET_ERROR          = 1,
    ADC_COLLCET_ERRORTIMEOUT   = 2,
    ADC_COLLCET_ERRORPARAMETER = 3,
    ADC_COLLCET_ERRORSOURCE    = 4,
}adc_collect_xxx_ret_code_t;

typedef enum
{
  ADC_COLLECT_CHANNEL_1 = 0,
  ADC_COLLECT_CHANNEL_2 = 1,
  ADC_COLLECT_CHANNEL_3 = 2,
  ADC_COLLECT_CHANNEL_4 = 3,
  ADC_COLLECT_CHANNEL_5 = 4,
  ADC_COLLECT_CHANNEL_6 = 5,
  ADC_COLLECT_CHANNEL_7 = 6,
  ADC_COLLECT_CHANNEL_8 = 7,
  ADC_COLLECT_CHANNEL_QUANTITY, // AD7606通道数量
}adc_collect_channels_t;

#define DEFAULT_ADC_RANGE ADC_COLLECT_RANGE_10V

//******************************** Defines **********************************//


//******************************** Declaring ********************************//

#ifdef ADC_COLLECT_USE_INTERRUPT
/*                 From Core层：     中断 接口                                */
typedef struct
{
    // 初始化函数指针
    int8_t (*pfinit)                     (void);
    // 反初始化函数指针
    int8_t (*pfdeinit)                   (void);
    // 使能中断函数指针
    int8_t (*pfenable_irq)               (void);
    // 禁用中断函数指针
    int8_t (*pfdisable_irq)              (void);
    // 使能外设时钟函数指针
    int8_t (*pfenable_peripheral_clock)  (void);
    // 禁用外设时钟函数指针
    int8_t (*pfdisable_peripheral_clock) (void);
}system_interrupt_interface_t;
/*                 From Core层：     中断 接口                                */

/*                 From Core层：     中断回调函数接口                          */
typedef struct 
{
    int8_t (**timer_irq_callbackfun)(void*);
    void   const **p_params;
}adc_collect_callback_fun_t;
/*                 From Core层：     中断回调函数接口                          */
#endif //END OF ADC_COLLECT_USE_INTERRUPT


/*                 From Core层：     时基接口                                 */
typedef struct
{
    uint32_t (*pfget_timetick_ms) (void);
}system_timebase_interface_t;
/*                 From Core层：     时基接口                                 */



/*                 From OS层：       RTOS 接口                                */
typedef void (*task_function_t)(void *);
// 定义一个结构体，用于表示RTOS的接口
typedef struct adc_collect_handler_os_interface_t
{
    // 创建任务
    adc_collect_handler_os_ret_code_t (*rtos_task_create)               (task_function_t task_function,
                                                                         const char * const task_name,
                                                                         const uint16_t stack_size,
                                                                         void * const task_argument,
                                                                         uint32_t priority,
                                                                         void ** const task_handle);
    //队列创建  
    adc_collect_handler_os_ret_code_t (*rtos_queue_create)              (void ** const pqueue,
                                                                         uint32_t queue_size,
                                                                         uint32_t item_size);
    //队列删除                                                                    
    adc_collect_handler_os_ret_code_t (*rtos_queue_delete)              (void * const pqueue);
    //队列发送  
    adc_collect_handler_os_ret_code_t (*rtos_queue_send)                (void * const pqueue,
                                                                         void * const item,
                                                                         uint32_t timeout);
    //队列接收  
    adc_collect_handler_os_ret_code_t (*rtos_queue_receive)             (void * const pqueue,
                                                                         void * const item,
                                                                         uint32_t timeout);
// 创建二进制信号量 
    adc_collect_handler_os_ret_code_t (*rtos_semphorebinary_create)     (void ** const psemphore);
// 创建互斥量
    adc_collect_handler_os_ret_code_t (*rtos_mutex_create)              (void ** const pmutex);
// 删除信号量   
    adc_collect_handler_os_ret_code_t (*rtos_semphore_delete)           (void * const psemphore);
// 获取信号量   
    adc_collect_handler_os_ret_code_t (*rtos_semphore_take)             (void * const psemphore,                                                                      uint32_t timeout);
// 释放信号量   
    adc_collect_handler_os_ret_code_t (*rtos_semphore_give)             (void * const psemphore);
// 中断中释放信号量 
    adc_collect_handler_os_ret_code_t (*rtos_semphore_give_formisr)     (void *const psemphore);
//任务通知，实现信号量    
    adc_collect_handler_os_ret_code_t (*rtos_task_notifiy_give_fromisr) (void * const task_handle);
//任务等待，实现信号量    
    adc_collect_handler_os_ret_code_t (*rtos_task_notifiy_take)         (uint32_t timeout);
//清空任务通知    
    adc_collect_handler_os_ret_code_t (*rtos_task_notifiy_clear)        (void * const task_handle);
// 获取任务句柄 
    adc_collect_handler_os_ret_code_t (*rtos_task_get_handle)           (void **const task_handle);
}adc_collect_handler_os_interface_t;
/*                 From OS层：       RTOS 接口                                */
/*                 From Core层：     开启定时器接口                            */
typedef struct 
{
    adc_collect_handler_os_ret_code_t (*pfstart_timer_collect)(void);  /* 开始采集数据接口 */
    adc_collect_handler_os_ret_code_t (*pfstop_timer_collect) (void);  /* 停止采集数据接口 */
}adc_collect_timer_collect_t;
/*                 From Core层：     开启定时器接口                            */

/*                 From Core层：    外部的栈空间分配                           */
/* 由外部决定分配的栈空间大小(系统集成层决定)
*  adc_collect_event_thread线程在创建的时候已确定，无需在进行分配
*/
typedef struct
{
    uint8_t  adc_collect_set_event_thread_proirity;
    uint16_t adc_collect_set_event_thread_stack_size;
    uint16_t adc_collect_event_queue_size;
    uint16_t adc_collect_set_event_queue_size;
}adc_collect_system_config_t;

/*                 From Core层：     输入参数接口                              */
// 定义一个结构体，用于存储ADC采集处理器的所有输入参数
typedef struct
{
    adc_collect_system_config_t        *p_system_config;
    // AD7606驱动器输入参数指针
    ad7606_driver_input_t              *p_ad7606_driver_input;
    //系统时基接口指针
    system_timebase_interface_t        *p_system_timebase_interface;
#ifdef ADC_COLLECT_USE_INTERRUPT
    // 系统中断接口指针
    system_interrupt_interface_t       *p_system_interrupt_interface;
    // ADC采集回调函数指针
    adc_collect_callback_fun_t         *p_adc_collect_callback_fun;
    // ADC采集定时器接口指针
    adc_collect_timer_collect_t        *p_adc_collect_timer_collect;
#endif //END OF ADC_COLLECT_USE_INTERRUPT
    // ADC采集处理器OS接口指针
    adc_collect_handler_os_interface_t *p_adc_collect_handler_os_interface;
    // 临界区接口
    ring_buff_rtos_critical_t 	       *pring_buff_rtos_critical;
    //动态分配内存
    ring_buff_dynamic_allocation_t     *pring_buff_dynamic_allocation;
}adc_collect_handler_input_all_arg_t;
/*                 From Core层：     输入参数接口                              */

/*                 adc_collect_handler句柄                                    */
typedef struct adc_collect_handler_private_data_t adc_collect_handler_private_data_t;
typedef struct bsp_adc_collect_xxx_handler_t
{
    // 驱动层接口
    ad7606_driver_input_t              *p_ad7606_driver_input;
    //系统时基接口
    system_timebase_interface_t        *p_system_timebase_interface;
    // 临界区接口
    ring_buff_rtos_critical_t 	       *pring_buff_rtos_critical;
    //动态分配内存
    ring_buff_dynamic_allocation_t     *pring_buff_dynamic_allocation;
#ifdef ADC_COLLECT_USE_INTERRUPT
    //中断接口
    system_interrupt_interface_t       *p_system_interrupt_interface;
    // ADC采集回调函数接口
    adc_collect_callback_fun_t         *p_adc_collect_callback_fun;
    // ADC采集定时器接口
    adc_collect_timer_collect_t        *p_adc_collect_timer_collect;
#endif //END OF ADC_COLLECT_USE_INTERRUPT
    // RTOS接口
    adc_collect_handler_os_interface_t *p_adc_collect_handler_os_interface;
    //AD7606驱动实例
    bsp_ad7606_driver_t                *p_bsp_ad7606_instance;
    // 系统配置信息
    adc_collect_system_config_t        *p_system_config;
    // ADC采集任务线程
    void                               *adc_collect_event_thread;
    // ADC采集任务事件队列
    void                               *adc_collect_event_queue;
#ifdef ADC_COLLECT_USE_INTERRUPT
    // ADC采集任务信号量
    void                               *event_handler_semphore;
    // ADC采集任务设置事件队列
    void                               *adc_collect_set_event_queue;
    void                               *adc_collect_set_event_thread;
    void                               *adc_collect_handler_mutex;  
#endif //END OF ADC_COLLECT_USE_INTERRUPT
    
    // ADC采集任务私有数据
    adc_collect_handler_private_data_t *p_private_data;
}bsp_adc_collect_xxx_handler_t;
/*                 adc_collect_handler句柄                                    */

typedef void (*pf_adc_collect_event_callback_fun_t)(void*const);

/*                 adc_collect事件句柄                                        */
//设置事件
typedef struct 
{
       
#ifndef ADC_COLLECT_USE_INTERRUPT
        adc_collect_channels_t               start_chennel;
        uint8_t                              channels;
        float                                *p_data;
        void                                 *p_input_pram;
        pf_adc_collect_event_callback_fun_t  pf_event_callback_fun;
#endif //END                                OF ADC_COLLECT_USE_INTERRUPT
       adc_collect_set_range_t              range;
       adc_collect_xxx_event_data_t         event_data;
       adc_collect_set_ovrsampling_t        ovrsampling;
       uint32_t                             timestamp;   
}adc_collect_xxx_set_event_t;

#ifdef ADC_COLLECT_USE_INTERRUPT
//输出事件
typedef struct
{
    adc_collect_channels_t start_chennel;
    uint8_t channels;
    float   data[VALID_ADC_CHANNEL_NUM];
    uint32_t timestamp;
}adc_collect_xxx_output_event_t;
#endif //END OF ADC_COLLECT_USE_INTERRUPT
/*                 adc_collect事件句柄                                        */



void adc_collect_event_thread(void *argument);

adc_collect_xxx_ret_code_t bsp_adc_collect_read_or_set(adc_collect_xxx_set_event_t* p_event);
adc_collect_xxx_ret_code_t bsp_adc_collect_output_data(adc_collect_xxx_output_event_t* p_event);
//******************************** Declaring ********************************//


#endif // __BSP_ADC_COLLECT_XXX_HANDLER_H__