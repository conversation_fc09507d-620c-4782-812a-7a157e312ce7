#include "timestamp_utils.h"
#include <stdio.h>
#include <string.h>

/* 数学常量 */
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

/**
 * @brief 计算频率（基于周期ticks）
 */
float timestamp_utils_calc_frequency(uint16_t period_ticks, uint32_t timer_freq_hz)
{
    if (period_ticks == 0) {
        return 0.0f;
    }
    return (float)timer_freq_hz / (float)period_ticks;
}

/**
 * @brief 频率有效性检查
 */
bool timestamp_utils_is_frequency_valid(float frequency, float min_freq, float max_freq)
{
    return (frequency >= min_freq && frequency <= max_freq);
}

/**
 * @brief 频率低通滤波
 */
float timestamp_utils_filter_frequency(float current_freq, float new_freq, float alpha)
{
    if (alpha < 0.0f) alpha = 0.0f;
    if (alpha > 1.0f) alpha = 1.0f;
    
    return current_freq * (1.0f - alpha) + new_freq * alpha;
}

/**
 * @brief 计算相位偏移
 */
timestamp_utils_ret_t timestamp_utils_calc_phase_correction(
    uint16_t t_sample0,
    uint16_t t_zero,
    float frequency,
    uint32_t timer_freq_hz,
    timestamp_phase_correction_t *correction)
{
    if (correction == NULL || frequency <= 0.0f || timer_freq_hz == 0) {
        return TIMESTAMP_UTILS_ERROR_PARAM;
    }

    /* 计算时间差 - 16位定时器 */
    uint16_t time_diff_ticks = timestamp_utils_calc_timer_diff(t_sample0, t_zero);
    
    /* 转换为微秒 */
    float time_offset_us = (float)time_diff_ticks * 1000000.0f / (float)timer_freq_hz;
    
    /* 计算周期 */
    float period_us = 1000000.0f / frequency;
    
    /* 计算相位偏移（度） */
    float phase_offset_deg = (time_offset_us / period_us) * 360.0f;
    
    /* 归一化到0-360度 */
    while (phase_offset_deg >= 360.0f) {
        phase_offset_deg -= 360.0f;
    }
    while (phase_offset_deg < 0.0f) {
        phase_offset_deg += 360.0f;
    }
    
    /* 转换为弧度 */
    float phase_offset_rad = phase_offset_deg * M_PI / 180.0f;
    
    /* 计算采样点偏移 */
    uint32_t sample_offset = (uint32_t)(phase_offset_deg / 360.0f * frequency * 1024.0f / 6400.0f);
    
    /* 填充结果 */
    correction->phase_offset_deg = phase_offset_deg;
    correction->phase_offset_rad = phase_offset_rad;
    correction->time_offset_us = time_offset_us;
    correction->sample_offset = sample_offset;
    
    return TIMESTAMP_UTILS_OK;
}

/**
 * @brief 应用相位修正到FFT结果
 */
timestamp_utils_ret_t timestamp_utils_apply_phase_correction(
    float *fft_complex_data,
    uint16_t fft_length,
    const timestamp_phase_correction_t *correction)
{
    if (fft_complex_data == NULL || correction == NULL) {
        return TIMESTAMP_UTILS_ERROR_PARAM;
    }

    float phase_offset_rad = correction->phase_offset_rad;
    
    /* 对FFT结果应用相位修正 */
    for (uint16_t i = 0; i < fft_length; i++) {
        float real = fft_complex_data[2 * i];
        float imag = fft_complex_data[2 * i + 1];
        
        /* 计算幅值和相位 */
        float magnitude = sqrtf(real * real + imag * imag);
        float phase = atan2f(imag, real);
        
        /* 应用相位修正 */
        phase -= phase_offset_rad;
        
        /* 转换回实部和虚部 */
        fft_complex_data[2 * i] = magnitude * cosf(phase);
        fft_complex_data[2 * i + 1] = magnitude * sinf(phase);
    }

    return TIMESTAMP_UTILS_OK;
}

/**
 * @brief 初始化统计信息
 */
void timestamp_utils_init_statistics(timestamp_statistics_t *stats)
{
    if (stats == NULL) return;
    
    memset(stats, 0, sizeof(timestamp_statistics_t));
    stats->min_frequency = 1000.0f;  /* 初始化为很大的值 */
    stats->max_frequency = 0.0f;
}

/**
 * @brief 更新统计信息
 */
void timestamp_utils_update_statistics(timestamp_statistics_t *stats, float frequency, bool is_valid)
{
    if (stats == NULL) return;
    
    stats->total_captures++;
    
    if (is_valid) {
        stats->valid_captures++;
        
        /* 更新平均频率 */
        stats->avg_frequency = (stats->avg_frequency * (stats->valid_captures - 1) + frequency) / stats->valid_captures;
        
        /* 更新最小最大频率 */
        if (frequency < stats->min_frequency) {
            stats->min_frequency = frequency;
        }
        if (frequency > stats->max_frequency) {
            stats->max_frequency = frequency;
        }
    }
}

/**
 * @brief 打印统计信息
 */
void timestamp_utils_print_statistics(const timestamp_statistics_t *stats)
{
    if (stats == NULL) return;
    
    printf("Timestamp Statistics:\n");
    printf("  Total captures: %lu\n", stats->total_captures);
    printf("  Valid captures: %lu\n", stats->valid_captures);
    printf("  Overflow count: %lu\n", stats->overflow_count);
    printf("  Average frequency: %.3f Hz\n", stats->avg_frequency);
    printf("  Min frequency: %.3f Hz\n", stats->min_frequency);
    printf("  Max frequency: %.3f Hz\n", stats->max_frequency);
    if (stats->total_captures > 0) {
        printf("  Valid rate: %.1f%%\n", (float)stats->valid_captures * 100.0f / stats->total_captures);
    }
}

/**
 * @brief 打印压缩时间戳信息
 */
void timestamp_utils_print_packed_timestamp(uint32_t packed_timestamp, const char *prefix)
{
    uint16_t capture = timestamp_utils_unpack_capture(packed_timestamp);
    uint16_t t0 = timestamp_utils_unpack_t0(packed_timestamp);
    
    printf("%s: packed=0x%08X, capture=%u, t0=%u\n", 
           prefix ? prefix : "Timestamp", packed_timestamp, capture, t0);
}

/**
 * @brief 打印频率测量结果
 */
void timestamp_utils_print_freq_result(const timestamp_freq_result_t *result, const char *prefix)
{
    if (result == NULL) return;
    
    printf("%s: freq=%.3f Hz, period=%.1f us, ticks=%u, valid=%s, updates=%lu\n",
           prefix ? prefix : "Frequency",
           result->frequency_hz,
           result->period_us,
           result->period_ticks,
           result->valid ? "Yes" : "No",
           result->update_count);
}
