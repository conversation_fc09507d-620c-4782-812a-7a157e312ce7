/******************************************************************************
 * @file system_adaption.h
 * @brief 
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-15
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow:
 * 
 * @par dependencies
 * 
 * @note 1 tab == 4 spaces!
 * 
 *****************************************************************************/
#ifndef __SYSTEM_ADAPTION_H__
#define __SYSTEM_ADAPTION_H__

//******************************** Includes *********************************//

/* 1.C库标准库头文件 */
#include <stdio.h>


/* 2. MCU头文件 */
/* 2.1 CPU驱动层头文件 */
#include "cmsis_os.h"
/* 2.2 Core层头文件 */
#include "main.h"
#include "spi.h"
#include "gpio.h"
#include "tim.h"
#include "spi.h"

#include "mcu_spi_driver.h"
#include "mcu_gpio_driver.h"
#include "mcu_uart_driver.h"
/* 3. OS层头文件 */
#include "os_adaptation.h"

/* 4. BSP驱动层头文件*/
#include "bsp_adc_collect_xxx_handler.h"
#include "rs485_event_handler.h"
#include "flash_event_handler.h"
#include "flash_event_fun.h"
#include "thunderstrike_event_handler.h"
#include "bsp_led_handler.h"
#include "uart_led_indicator.h"
/* 5. 应用层头文件 */
#include "data_process_task.h"
#include "data_proc_calcu_fun.h"
#include "fft_calculate_task.h"

/* 6.中间件头文件 */
#include "SEGGER_RTT.h"
#include "system_config.h"
//******************************** Includes *********************************//

//******************************** Defines **********************************//


#ifdef DEBUG_SYSTEM_ADAPTION
//#define SYSTEM_ADAPTION_LOG_PR
//#define SYSTEM_ADAPTION_LOG_D
#define SYSTEM_ADAPTION_LOG_I
#define SYSTEM_ADAPTION_LOG_E
#endif /* DEBUG_SYSTEM_ADAPTION */

#ifdef  DEBUG_SYSTEM_ADAPTION
#ifdef SYSTEM_ADAPTION_LOG_PR
#define SYSTEM_ADAPTION_DEBUG_PR(...)  printf(__VA_ARGS__)
#else
#define SYSTEM_ADAPTION_DEBUG_PR(...)
#endif/* SYSTEM_ADAPTION_LOG_D */

#ifdef SYSTEM_ADAPTION_LOG_D
#define SYSTEM_ADAPTION_LOG_DEUBG(...) log_d(__VA_ARGS__)
#else
#define SYSTEM_ADAPTION_LOG_DEUBG(...)
#endif/* SYSTEM_ADAPTION_LOG_D */

#ifdef SYSTEM_ADAPTION_LOG_E
#define SYSTEM_ADAPTION_LOG_ERROR(...) log_e(__VA_ARGS__)
#else
#define SYSTEM_ADAPTION_LOG_ERROR(...)
#endif/* SYSTEM_ADAPTION_LOG_E */

#ifdef SYSTEM_ADAPTION_LOG_I
#define SYSTEM_ADAPTION_LOG_INFO(...)  log_irq_pr(__VA_ARGS__)
#else
#define SYSTEM_ADAPTION_LOG_INFO(...)
#endif/* SYSTEM_ADAPTION_LOG_I */


#else
#define SYSTEM_ADAPTION_DEBUG_PR(...)
#define SYSTEM_ADAPTION_LOG_DEUBG(...)
#define SYSTEM_ADAPTION_LOG_ERROR(...)
#endif /* DEBUG_SYSTEM_ADAPTION */

//#define SYSTEM_VISUAL_OUTPUT_ENABLE /* 系统可视化输出使能*/

#define CONFIG_USE_CTR_AMP  0 /* 是否使用控制放大器 */

/* SPI回调函数宏替换 */
#define  SPI_TX_COMPLETE_CALLBACK_FUN  void HAL_SPI_TxCpltCallback(SPI_HandleTypeDef *hspi)
#define  SPI_RX_COMPLETE_CALLBACK_FUN  void HAL_SPI_RxCpltCallback(SPI_HandleTypeDef *hspi)
/* EXTI回调函数宏替换 */
#define  GPIO_EXTI_CALLBACK_FUN        void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)

/* 定时器输入捕获回调函数宏替换 */
#define  TIM_IC_CALLBACK_FUN          void HAL_TIM_IC_CaptureCallback(TIM_HandleTypeDef *htim)
typedef enum
{
    SYS_ADAPTION_RTOS_PDFALSE = 0,
    SYS_ADAPTION_RTOS_PDTRUE  = 1,
}sys_adaption_os_ret_t;

typedef enum
{
  SYS_ADAPTION_OK                = 0,         /* Operation completed successfully.  */
  SYS_ADAPTION_ERROR             = 1,         /* Run-time error without case matched*/
  SYS_ADAPTION_ERRORTIMEOUT      = 2,         /* Operation failed with timeout      */
  SYS_ADAPTION_ERRORRESOURCE     = 3,         /* Resource not available.            */
  SYS_ADAPTION_ERRORPARAMETER    = 4,         /* Parameter error.                   */
  SYS_ADAPTION_ERRORNOMEMORY     = 5,         /* Out of memory.                     */
  SYS_ADAPTION_ERRORISR          = 6,         /* Not allowed in ISR context         */
  SYS_ADAPTION_RESERVED          = 0x7FFFFFFF /* Reserved                           */
}sys_adaption_ret_t;
//******************************** Defines **********************************//

//******************************** Declaration ******************************//
/* 各线程任务栈大小定义 */
typedef struct 
{
  uint16_t adc_collect_event_thread_stack_size;               
  uint16_t adc_collect_set_event_thread_stack_size;                         
  uint16_t async_output_thread_stack_size;     
  uint16_t ctrl_amp_handler_thread_stack_size;                                 
  uint16_t data_proc_handler_thread_stack_size;                
  uint16_t fft_collection_data_thread_stack_size;
  uint16_t fft_event_handler_thread_stack_size;              
  uint16_t flash_event_handler_thread_stack_size;
  uint16_t rs485_event_handle_thread_stack_size;
  uint16_t rs485_output_msg_thread_stack_size;
  uint16_t rtc_handler_thread_stack_size;
  uint16_t rtc_timestamp_updata_to_rs485_reg_thread_stack_size;
  uint16_t system_visual_output_msg_stack_size;
  uint16_t thunderstrike_count_thread_stack_size;
  uint16_t led_handler_thread_stack_size;
  uint16_t zero_detect_task_stack_size;                   /* 零点检测任务栈大小 */
}sys_adaption_task_stack_t;

/* 各线程任务优先级定义 */
typedef struct 
{
  uint8_t adc_collect_event_thread_priority;                    
  uint8_t adc_collect_set_event_thread_priority;                 
  uint8_t async_output_thread_priority;                                            
  uint8_t data_proc_handler_thread_priority;
  uint8_t fft_event_handler_thread_priority;                     
  uint8_t fft_collection_data_thread_priority;                   
  uint8_t flash_event_handler_thread_priority;
  uint8_t rs485_event_handle_thread_priority;
  uint8_t rs485_output_msg_thread_priority;
  uint8_t system_visual_output_msg_priority;
  uint8_t led_handler_thread_priority;
  uint8_t zero_detect_task_priority;                      /* 零点检测任务优先级 */
}sys_adaption_task_priority_t;

/* 各线程任务队列大小定义 */
typedef struct 
{
  uint16_t adc_collect_event_queue_size;          
  uint16_t adc_collect_set_event_queue_size; 
  uint16_t data_proc_handler_queue_size;   
  uint16_t fft_finish_collection_queue_size;
  uint16_t flash_event_queue_size;
  uint16_t rs485_event_queue_size;
  uint16_t rtc_handler_event_queue_size;
  uint16_t led_handler_event_queue_size;
}sys_adpation_queue_size_t;

typedef int8_t (*pf_callback_fun_t)(void*);
//******************************** Declaration ******************************//


//******************************** Variables ********************************//
typedef int8_t (*pf_callback_fun_t)(void*);

//******************************** Variables ********************************//
//******************************** Functions ********************************//
sys_adaption_ret_t system_source_inst(void);

/* LED handler接口 */
bsp_led_handler_t* get_led_handler_instance(void);

/* 零点检测任务接口 */
sys_adaption_ret_t zero_detect_task_inst(void);



#endif /* __SYSTEM_ADAPTION_H__ */