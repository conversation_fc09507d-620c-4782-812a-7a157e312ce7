/******************************************************************************
 * @file timestamp_driver.h
 * @brief 时间戳硬件驱动层接口
 * <AUTHOR> Assistant
 * @version 1.0
 * @date 2024-12-19
 *
 * @copyright Copyright (c) 2024
 *
 * Processing flow:
 * 根据bsp_timestamp_driver_instance函数构造实例
 * 注意事项：
 * 1.输入的TIM接口必须为数组
 * 2.构造的数组内容必须按timestamp_tim_quantity_t来排列
 * 3.专注于底层硬件操作，不包含业务逻辑
 *
 * @par dependencies
 * stdint.h
 * elog.h
 * linked_list.h
 * system_config.h
 *
 * @note 1 tab == 4 spaces!
 *
 *****************************************************************************/
#ifndef __TIMESTAMP_DRIVER_H__
#define __TIMESTAMP_DRIVER_H__

//******************************** Includes *********************************//
#include <stdint.h>
#include <string.h>
#include "elog.h"
#include "linked_list.h"
#include "system_config.h"
//******************************** Includes *********************************//

//******************************** Defines **********************************//
#define TIMESTAMP_DRIVER_USE_INTERRUPT
#define TIMESTAMP_DRIVER_USE_CAPTURE_COUNT
/* 调试宏定义已迁移到 system_config.h 中统一管理 */

#ifdef TIMESTAMP_DRIVER_DEBUG
    #define TIMESTAMP_DRIVER_LOG_IRQ
#endif /*  TIMESTAMP_DRIVER_DEBUG*/

#ifdef TIMESTAMP_DRIVER_DEBUG
    #define TIMESTAMP_DRIVER_DEBUG_PR(...) printf(__VA_ARGS__)

#ifdef TIMESTAMP_DRIVER_LOG_D
    #define TIMESTAMP_DRIVER_LOG_DEUBG(...) log_d(__VA_ARGS__)
#else
    #define TIMESTAMP_DRIVER_LOG_DEUBG(...)
#endif  /* TIMESTAMP_DRIVER_LOG_D */

#define TIMESTAMP_DRIVER_LOG_ERROR(...) log_e(__VA_ARGS__)

#ifdef TIMESTAMP_DRIVER_LOG_IRQ
    #define TIMESTAMP_DRIVER_LOG_IRQ(...)  log_irq_pr(__VA_ARGS__)
#else
    #define TIMESTAMP_DRIVER_LOG_IRQ(...)
#endif  /* TIMESTAMP_DRIVER_LOG_IRQ */

#else
#define TIMESTAMP_DRIVER_DEBUG_PR(...)
#define TIMESTAMP_DRIVER_LOG_DEUBG(...)
#define TIMESTAMP_DRIVER_LOG_ERROR(...)
#endif

#define TIMESTAMP_RTOS_SUPPORTING

typedef enum
{
    TIM_CAPTURE_RISING  = 0u,    // 上升沿捕获
    TIM_CAPTURE_FALLING = 1,     // 下降沿捕获
    TIM_CAPTURE_BOTH    = 2,     // 双边沿捕获
    TIM_CAPTURE_ERROR,
}tim_interface_capture_edge_t;

#ifdef TIMESTAMP_RTOS_SUPPORTING
typedef enum
{
  TIMESTAMP_RTOS_PDFALSE = 0, // 返回值为假
  TIMESTAMP_RTOS_PDTRUE  = 1, // 返回值为真
}timestamp_rtos_ret_code_t;
#endif //END OF TIMESTAMP_RTOS_SUPPORTING

typedef enum
{
  TIMESTAMP_TIM_GRID_ZERO = 0,      /* TIM4 - 电网零点捕获 */
  TIMESTAMP_TIM_SIGNAL_FREQ = 1,    /* TIM3 - 信号频率检测 */
  TIMESTAMP_TIM_QUANTITY,           /* 定时器数量 */
}timestamp_tim_quantity_t;

typedef enum
{
  TIMESTAMP_OK             = 0,
  TIMESTAMP_ERROR          = 1,
  TIMESTAMP_ERRORTIMEOUT   = 2,
  TIMESTAMP_ERRORPARAMETER = 3,
}timestamp_driver_ret_code_t;
//******************************** Defines **********************************//

//******************************** Declaration ******************************//
/*                 From OS层：       RTOS 接口                                */
#ifdef TIMESTAMP_RTOS_SUPPORTING
typedef void (*task_function_t)(void *);
// 定义一个结构体，用于表示RTOS的接口
typedef struct timestamp_rtos_interface_t
{
    //队列创建
    timestamp_rtos_ret_code_t (*rtos_queue_create)          (void ** const pqueue,
                                                            uint32_t queue_size,
                                                            uint32_t item_size);
    //队列删除
    timestamp_rtos_ret_code_t (*rtos_queue_delete)          (void * const pqueue);
    //队列发送
    timestamp_rtos_ret_code_t (*rtos_queue_send)            (void * const pqueue,
                                                           void * const item,
                                                           uint32_t timeout);
    //队列接收
    timestamp_rtos_ret_code_t (*rtos_queue_receive)         (void * const pqueue,
                                                           void * const item,
                                                           uint32_t timeout);
    // 创建任务
    timestamp_rtos_ret_code_t (*rtos_task_create)           (task_function_t task_function,
                                                           const char * const task_name,
                                                           const uint16_t stack_size,
                                                           void * const task_argument,
                                                           uint32_t priority,
                                                           void ** const task_handle);
}timestamp_rtos_interface_t;
#endif //END OF TIMESTAMP_RTOS_SUPPORTING

/*                 From 硬件层：     TIM接口                                  */
typedef struct tim_interface_t
{
    void *ptim_handler;                                     /* 定时器句柄 */
    timestamp_driver_ret_code_t (*pftim_init)              (void *ptim_handler);
    timestamp_driver_ret_code_t (*pftim_deinit)            (void *ptim_handler);
    timestamp_driver_ret_code_t (*pftim_start_capture)     (void *ptim_handler, uint32_t channel);
    timestamp_driver_ret_code_t (*pftim_stop_capture)      (void *ptim_handler, uint32_t channel);
    timestamp_driver_ret_code_t (*pftim_get_counter)       (void *ptim_handler, uint16_t *count_value);
    timestamp_driver_ret_code_t (*pftim_get_capture_value) (void *ptim_handler, uint32_t channel, uint16_t *capture_value);
    timestamp_driver_ret_code_t (*pftim_set_counter)       (void *ptim_handler, uint16_t count_value);
}tim_interface_t;

/*                 From 动态分配层： 动态分配接口                              */
typedef struct timestamp_dynamic_allocation_t
{
    void* (*pfmalloc)(uint32_t size);
    void  (*pffree)(void* ptr);
}timestamp_dynamic_allocation_t;

/*                 From 回调层：     中断回调接口                              */
typedef struct timestamp_capture_irq_callback_t
{
    int8_t (**pf_capture_callback_fun)(void *p_arg);       /* 捕获中断回调函数指针的指针 */
    void   **p_arg;                                        /* 回调函数参数指针的指针 */
}timestamp_capture_irq_callback_t;

/*                 From 功能层：     功能函数接口                              */
typedef int8_t (*pf_notify_capture_finish_fun_t)(void *p_arg);
typedef int8_t (*pf_get_timestamp_fun_t)(uint32_t *timestamp);

/* 捕获数据结构 */
typedef struct timestamp_capture_data_t
{
    uint16_t capture_value;                 /* 捕获值 */
    tim_interface_capture_edge_t edge_type; /* 边沿类型 */
    uint32_t system_tick;                   /* 系统tick */
    uint16_t capture_count;                 /* 捕获次数 */
    bool valid;                             /* 数据有效标志 */
} timestamp_capture_data_t;

/* 驱动状态结构 */
typedef struct timestamp_driver_status_t
{
    bool initialized;                       /* 初始化标志 */
    bool tim_grid_enabled;                  /* TIM4使能标志 */
    bool tim_signal_enabled;                /* TIM3使能标志 */
    uint32_t grid_capture_count;            /* 电网捕获次数 */
    uint32_t signal_capture_count;          /* 信号捕获次数 */
    uint32_t overflow_count;                /* 溢出次数 */
    uint32_t error_count;                   /* 错误次数 */
    uint16_t last_grid_capture_value;       /* 最后一次电网捕获值 */
    uint16_t last_signal_capture_value;     /* 最后一次信号捕获值 */
} timestamp_driver_status_t;

/* 驱动输入数据结构 */
typedef struct timestamp_driver_input_data_t
{
    tim_interface_t                      *ptim_interface;           /* TIM接口数组 */
    timestamp_dynamic_allocation_t       *pdynamic_allocation;      /* 动态分配接口 */
    timestamp_capture_irq_callback_t     *pcapture_irq_callback;   /* 捕获中断回调接口数组 */
    pf_notify_capture_finish_fun_t       pfnotify_capture_finish;  /* 捕获完成通知回调 */
    pf_get_timestamp_fun_t               pf_get_timestamp;          /* 获取时间戳接口 */
#ifdef TIMESTAMP_RTOS_SUPPORTING
    timestamp_rtos_interface_t           *prtos_interface;          /* RTOS接口 */
#endif
}timestamp_driver_input_data_t;

/* 驱动句柄结构 */
typedef struct bsp_timestamp_driver_t
{
    void                        *private_data;                     /* 私有数据指针 */
}bsp_timestamp_driver_t;
//******************************** Declaration ******************************//

//******************************** Variables ********************************//
//******************************** Functions ********************************//

/******************************************************************************
 * @brief 时间戳驱动实例化
 *
 * @param  pbsp_timestamp_driver    时间戳驱动实例
 * @param  pinput_data              输入数据
 *
 * @return timestamp_driver_ret_code_t
 * @retval TIMESTAMP_OK             实例化成功
 * @retval TIMESTAMP_ERROR          实例化失败
 * @retval TIMESTAMP_ERRORPARAMETER 参数错误
 *
 * @note 1. 实例化时间戳驱动
 *       2. 输入的TIM接口必须为数组，按timestamp_tim_quantity_t排列
 *       3. 输入的捕获中断回调接口必须为数组，按timestamp_tim_quantity_t排列
 *****************************************************************************/
timestamp_driver_ret_code_t bsp_timestamp_driver_instance(bsp_timestamp_driver_t *pbsp_timestamp_driver,
                                                         timestamp_driver_input_data_t *pinput_data);

/******************************************************************************
 * @brief 启动时间戳捕获
 *
 * @param  pbsp_timestamp_driver    时间戳驱动实例
 * @param  tim_type                 定时器类型
 * @param  channel                  定时器通道
 *
 * @return timestamp_driver_ret_code_t
 * @retval TIMESTAMP_OK             启动成功
 * @retval TIMESTAMP_ERROR          启动失败
 * @retval TIMESTAMP_ERRORPARAMETER 参数错误
 *****************************************************************************/
timestamp_driver_ret_code_t bsp_timestamp_driver_start_capture(bsp_timestamp_driver_t *pbsp_timestamp_driver,
                                                              timestamp_tim_quantity_t tim_type,
                                                              uint32_t channel);

/******************************************************************************
 * @brief 停止时间戳捕获
 *
 * @param  pbsp_timestamp_driver    时间戳驱动实例
 * @param  tim_type                 定时器类型
 * @param  channel                  定时器通道
 *
 * @return timestamp_driver_ret_code_t
 * @retval TIMESTAMP_OK             停止成功
 * @retval TIMESTAMP_ERROR          停止失败
 * @retval TIMESTAMP_ERRORPARAMETER 参数错误
 *****************************************************************************/
timestamp_driver_ret_code_t bsp_timestamp_driver_stop_capture(bsp_timestamp_driver_t *pbsp_timestamp_driver,
                                                             timestamp_tim_quantity_t tim_type,
                                                             uint32_t channel);

/******************************************************************************
 * @brief 获取定时器计数值
 *
 * @param  pbsp_timestamp_driver    时间戳驱动实例
 * @param  tim_type                 定时器类型
 * @param  count_value              输出计数值
 *
 * @return timestamp_driver_ret_code_t
 * @retval TIMESTAMP_OK             获取成功
 * @retval TIMESTAMP_ERROR          获取失败
 * @retval TIMESTAMP_ERRORPARAMETER 参数错误
 *****************************************************************************/
timestamp_driver_ret_code_t bsp_timestamp_driver_get_counter(bsp_timestamp_driver_t *pbsp_timestamp_driver,
                                                            timestamp_tim_quantity_t tim_type,
                                                            uint16_t *count_value);

/******************************************************************************
 * @brief 设置定时器计数值
 *
 * @param  pbsp_timestamp_driver    时间戳驱动实例
 * @param  tim_type                 定时器类型
 * @param  count_value              计数值
 *
 * @return timestamp_driver_ret_code_t
 * @retval TIMESTAMP_OK             设置成功
 * @retval TIMESTAMP_ERROR          设置失败
 * @retval TIMESTAMP_ERRORPARAMETER 参数错误
 *****************************************************************************/
timestamp_driver_ret_code_t bsp_timestamp_driver_set_counter(bsp_timestamp_driver_t *pbsp_timestamp_driver,
                                                            timestamp_tim_quantity_t tim_type,
                                                            uint16_t count_value);

/******************************************************************************
 * @brief 获取捕获值
 *
 * @param  pbsp_timestamp_driver    时间戳驱动实例
 * @param  tim_type                 定时器类型
 * @param  channel                  定时器通道
 * @param  capture_value            输出捕获值
 *
 * @return timestamp_driver_ret_code_t
 * @retval TIMESTAMP_OK             获取成功
 * @retval TIMESTAMP_ERROR          获取失败
 * @retval TIMESTAMP_ERRORPARAMETER 参数错误
 *****************************************************************************/
timestamp_driver_ret_code_t bsp_timestamp_driver_get_capture_value(bsp_timestamp_driver_t *pbsp_timestamp_driver,
                                                                  timestamp_tim_quantity_t tim_type,
                                                                  uint32_t channel,
                                                                  uint16_t *capture_value);

/******************************************************************************
 * @brief 获取驱动状态
 *
 * @param  pbsp_timestamp_driver    时间戳驱动实例
 * @param  status                   输出状态信息
 *
 * @return timestamp_driver_ret_code_t
 * @retval TIMESTAMP_OK             获取成功
 * @retval TIMESTAMP_ERROR          获取失败
 * @retval TIMESTAMP_ERRORPARAMETER 参数错误
 *****************************************************************************/
timestamp_driver_ret_code_t bsp_timestamp_driver_get_status(bsp_timestamp_driver_t *pbsp_timestamp_driver,
                                                           timestamp_driver_status_t *status);

/******************************************************************************
 * @brief 重置驱动统计信息
 *
 * @param  pbsp_timestamp_driver    时间戳驱动实例
 *
 * @return timestamp_driver_ret_code_t
 * @retval TIMESTAMP_OK             重置成功
 * @retval TIMESTAMP_ERROR          重置失败
 * @retval TIMESTAMP_ERRORPARAMETER 参数错误
 *****************************************************************************/
timestamp_driver_ret_code_t bsp_timestamp_driver_reset_statistics(bsp_timestamp_driver_t *pbsp_timestamp_driver);

/* 中断处理函数 - 供HAL层调用 */

/******************************************************************************
 * @brief 定时器捕获中断处理函数
 *
 * @param  pbsp_timestamp_driver    时间戳驱动实例
 * @param  tim_type                 定时器类型
 * @param  channel                  定时器通道
 * @param  capture_value            捕获值
 * @param  edge_type                边沿类型
 *
 * @return void
 *
 * @note 此函数在中断上下文中调用，应保持简洁高效
 *****************************************************************************/
void bsp_timestamp_driver_capture_irq_handler(bsp_timestamp_driver_t *pbsp_timestamp_driver,
                                             timestamp_tim_quantity_t tim_type,
                                             uint32_t channel,
                                             uint16_t capture_value,
                                             tim_interface_capture_edge_t edge_type);

/* 全局访问接口 - 供其他模块调用 */

/******************************************************************************
 * @brief 获取全局时间戳驱动实例
 *
 * @return bsp_timestamp_driver_t* 全局驱动实例指针，NULL表示未初始化
 *****************************************************************************/
bsp_timestamp_driver_t* bsp_timestamp_driver_get_global_instance(void);

/******************************************************************************
 * @brief 设置全局时间戳驱动实例
 *
 * @param  pbsp_timestamp_driver    时间戳驱动实例
 *
 * @return timestamp_driver_ret_code_t
 * @retval TIMESTAMP_OK             设置成功
 * @retval TIMESTAMP_ERRORPARAMETER 参数错误
 *****************************************************************************/
timestamp_driver_ret_code_t bsp_timestamp_driver_set_global_instance(bsp_timestamp_driver_t *pbsp_timestamp_driver);

//******************************** Functions ********************************//

#endif /* __TIMESTAMP_DRIVER_H__ */
