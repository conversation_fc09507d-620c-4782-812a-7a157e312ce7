# ZeroDetectTask 零点检测任务实现

## 概述

本实现基于文档 `embedded_sync_sampling_doc.md` 中"中断与时间戳模块"部分的 `ZeroDetectTask` 设计要求，使用现有的 BSP_Timestamp 模块代码创建了完整的零点检测任务系统。

## 功能特性

### 核心功能
- **零点捕获**：捕获市电过零点上升沿，记录时间戳和次数标号
- **频率分析**：周期性测量市电周期，计算并更新电网频率（每5个周期更新一次）
- **信号频率检测**：使用 TIM3 捕获采样信号频率
- **溢出处理**：当 capture_count 接近 65535 时自动触发重置流程
- **相位修正**：提供压缩时间戳生成和相位修正计算功能

### 技术特性
- **FreeRTOS 任务**：基于事件驱动的任务架构
- **中断安全**：支持从中断上下文发送事件
- **线程安全**：提供线程安全的全局访问接口
- **模块化设计**：清晰的分层架构，易于维护和扩展
- **集成接口**：与现有 timestamp_manager_v2 模块无缝集成

## 文件结构

```
BSP/BSP_Timestamp/
├── Inc/
│   ├── zero_detect_task.h          # 零点检测任务头文件
│   └── zero_detect_integration.h   # 集成接口头文件
└── Src/
    ├── zero_detect_task.c          # 零点检测任务实现
    ├── zero_detect_integration.c   # 集成接口实现
    └── zero_detect_example.c       # 使用示例
```

## 快速开始

### 1. 自动集成（推荐）

系统已经在 `system_adaption.c` 中集成了 ZeroDetectTask，只需在 main 函数中调用：

```c
#include "system_adaption.h"

int main(void)
{
    // ... 其他初始化代码 ...
    
    // 初始化系统资源（包括 ZeroDetectTask）
    if (system_source_inst() != SYS_ADAPTION_OK) {
        // 处理错误
    }
    
    // ... 启动调度器 ...
}
```

### 2. 手动集成

如果需要手动控制初始化过程：

```c
#include "zero_detect_integration.h"

// 使用默认配置初始化并启动
ZERO_DETECT_INTEGRATION_INIT_AND_START();

// 或者手动初始化
zero_detect_ret_t ret = zero_detect_integration_init(NULL, NULL);
if (ret == ZERO_DETECT_OK) {
    zero_detect_integration_start();
}
```

### 3. 获取零点检测信息

```c
// 获取当前电网频率
float grid_freq = ZERO_DETECT_GET_GRID_FREQ();

// 获取当前信号频率
float signal_freq = ZERO_DETECT_GET_SIGNAL_FREQ();

// 获取完整状态信息
zero_detect_status_t status;
if (zero_detect_get_global_status(&status) == ZERO_DETECT_OK) {
    printf("Grid Freq: %.2f Hz, Valid: %s\n", 
           status.grid_freq_hz, status.freq_valid ? "Yes" : "No");
}
```

### 4. 生成压缩时间戳（供 AD7606 使用）

```c
// 生成压缩时间戳
uint32_t timestamp = ZERO_DETECT_GENERATE_TIMESTAMP();

// 计算相位修正参数
timestamp_phase_correction_t correction;
if (zero_detect_integration_calculate_phase_correction(timestamp, &correction) == ZERO_DETECT_OK) {
    // 使用相位修正参数
}
```

## 配置参数

### 任务配置
- **栈大小**：512 字节（在 system_adaption.c 中配置）
- **优先级**：osPriorityAboveNormal（在 system_adaption.c 中配置）
- **队列大小**：10 个事件

### 频率检测配置
- **标称频率**：50.0 Hz
- **有效频率范围**：45.0 ~ 65.0 Hz
- **频率更新周期**：每 5 个零点周期
- **溢出阈值**：0xFFFC (65532)

## API 参考

### 全局访问接口

```c
// 获取电网频率
float zero_detect_get_grid_frequency(void);

// 获取信号频率
float zero_detect_get_signal_frequency(void);

// 获取零点时间戳
zero_detect_ret_t zero_detect_get_latest_zero_timestamp(uint16_t *capture_count, uint16_t *t_zero);

// 获取完整状态
zero_detect_ret_t zero_detect_get_global_status(zero_detect_status_t *status);
```

### 集成接口

```c
// 初始化集成系统
zero_detect_ret_t zero_detect_integration_init(const zero_detect_config_t *zero_detect_config,
                                              const timestamp_manager_config_t *timestamp_config);

// 启动/停止集成系统
zero_detect_ret_t zero_detect_integration_start(void);
zero_detect_ret_t zero_detect_integration_stop(void);

// 生成压缩时间戳
uint32_t zero_detect_integration_generate_packed_timestamp(void);

// 相位修正计算
zero_detect_ret_t zero_detect_integration_calculate_phase_correction(uint32_t packed_timestamp,
                                                                   timestamp_phase_correction_t *correction);
```

### 便捷宏

```c
// 初始化并启动
ZERO_DETECT_INTEGRATION_INIT_AND_START()

// 停止并反初始化
ZERO_DETECT_INTEGRATION_STOP_AND_DEINIT()

// 获取频率
ZERO_DETECT_GET_GRID_FREQ()
ZERO_DETECT_GET_SIGNAL_FREQ()

// 生成时间戳
ZERO_DETECT_GENERATE_TIMESTAMP()
```

## 工作原理

### 1. 中断处理流程
1. TIM4 捕获市电零点上升沿触发中断
2. timestamp_manager_v2 处理中断，计算频率
3. 通过回调函数将事件发送到 ZeroDetectTask 队列
4. ZeroDetectTask 处理事件，更新状态信息

### 2. 频率计算
- 每次零点捕获时记录时间戳
- 每 5 个周期计算一次平均频率
- 验证频率是否在有效范围内（45-65 Hz）
- 更新全局频率状态

### 3. 时间戳管理
- capture_count：16位零点次数计数器
- t_zero：最近一次零点的16位定时器计数值
- 压缩时间戳：(capture_count << 16) | t_sample0

### 4. 溢出处理
- 监控 capture_count 接近 65535
- 自动触发"时间戳归零与缓存重置"流程
- 通知其他模块进行相应处理

## 调试和诊断

### 状态打印
```c
// 打印集成状态
zero_detect_integration_print_status();

// 打印任务状态
zero_detect_task_print_status(&handle);
```

### 自检功能
```c
// 执行自检
zero_detect_ret_t ret = zero_detect_task_self_test(&handle);
```

### 调试日志
通过 `TIMESTAMP_DEBUG_ENABLE` 宏控制调试输出：
- 零点捕获事件
- 频率更新信息
- 错误和异常情况

## 注意事项

1. **初始化顺序**：确保在启动 FreeRTOS 调度器之前完成初始化
2. **中断优先级**：确保定时器中断优先级设置正确
3. **内存使用**：任务栈大小已优化，但可根据实际需求调整
4. **频率范围**：超出 45-65 Hz 范围的频率将被标记为无效
5. **溢出处理**：系统会自动处理溢出，但需要确保其他模块能正确响应

## 扩展和定制

### 自定义配置
```c
zero_detect_config_t config = zero_detect_get_default_config();
config.freq_update_cycles = 10;  // 改为每10个周期更新
config.min_valid_freq_hz = 40.0f; // 扩大有效频率范围
// ... 其他配置 ...
zero_detect_integration_init(&config, NULL);
```

### 自定义回调
```c
void my_zero_cross_callback(uint16_t capture_count, uint16_t t_zero, float frequency, void *user_data) {
    // 自定义处理逻辑
}

zero_detect_callbacks_t callbacks = {
    .on_zero_cross = my_zero_cross_callback,
    // ... 其他回调 ...
};
zero_detect_task_register_callbacks(&handle, &callbacks);
```

## 示例代码

详细的使用示例请参考 `zero_detect_example.c` 文件，包含：
- 基本初始化和使用
- 状态查询
- 时间戳生成
- 相位修正计算
- 回调函数注册
- 完整使用流程

## 版本信息

- **版本**：1.0
- **创建日期**：2024-12-19
- **兼容性**：基于现有 BSP_Timestamp 模块
- **依赖**：FreeRTOS, timestamp_manager_v2
