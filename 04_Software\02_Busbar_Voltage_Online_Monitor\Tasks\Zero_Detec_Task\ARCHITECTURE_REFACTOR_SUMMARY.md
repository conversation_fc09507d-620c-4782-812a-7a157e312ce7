# 零点检测任务模块架构重构总结

## 重构目标
参考 `bsp_adc_collect_xxx_handler.c` 文件中 `adc_collect_event_thread` 函数的线程初始化流程，对零点检测任务模块进行架构重构，实现线程内部资源管理和强制观察者模式。

## 参考架构分析

### ADC采集模块的关键设计模式
通过分析 `adc_collect_event_thread` 函数，发现了以下关键架构特点：

1. **线程内部资源管理**：
   - Handler句柄和private_data在线程内部创建（局部变量和静态变量）
   - 资源生命周期与线程保持一致
   - 使用 `static adc_collect_handler_private_data_t private_data` 模式

2. **实例化函数封装**：
   - `bsp_adc_collect_xxx_handler_inst()` 函数为static，不对外开放
   - 实例化过程完全在线程内部执行
   - 使用 `_mount_handler()` 函数挂载全局指针

3. **全局指针访问模式**：
   - 使用 `static bsp_adc_collect_xxx_handler_t *gp_adc_collect_xxx_handler = NULL`
   - 外部通过全局指针访问handler实例
   - 严格的状态检查和错误处理

## 重构实现

### 1. 头文件接口重构

**重构前的对外接口：**
```c
/* Handler接口函数声明 */
zero_detect_ret_t zero_detect_handler_register_callback(...);
zero_detect_ret_t zero_detect_handler_get_status(...);
zero_detect_ret_t zero_detect_handler_get_grid_frequency(...);
zero_detect_ret_t zero_detect_handler_get_signal_frequency(...);

/* 全局访问接口 */
zero_detect_ret_t zero_detect_global_init(...);
zero_detect_ret_t zero_detect_global_start(void);
// ... 其他全局函数
```

**重构后的对外接口：**
```c
/* 观察者模式接口 - 外部模块注册回调函数 */
zero_detect_ret_t zero_detect_register_callback(const zero_detect_callback_t *callback);
zero_detect_ret_t zero_detect_unregister_callback(const zero_detect_callback_t *callback);

/* 线程启动接口 */
void zero_detect_handler_thread(void *argument);

/* 工具函数 */
zero_detect_system_config_t zero_detect_get_default_system_config(void);
const char* zero_detect_get_error_string(zero_detect_ret_t error);
```

### 2. 全局变量重构

**重构前：**
```c
/* 全局零点检测句柄 - 基于新架构 */
static zero_detect_handler_t g_zero_detect_handler = {0};
static bool g_zero_detect_global_initialized = false;
```

**重构后：**
```c
/* 全局零点检测句柄指针 - 参考ADC模块模式 */
static zero_detect_handler_t *gp_zero_detect_handler = NULL;
```

### 3. 线程函数完全重构

**重构前的线程函数：**
- 接收 `zero_detect_handler_t` 指针参数
- 依赖外部创建的handler实例
- 简单的事件处理循环

**重构后的线程函数：**
```c
void zero_detect_handler_thread(void *argument)
{
    /* 0. 定义相关变量 */
    zero_detect_handler_input_all_arg_t *p_input_arg = 
                                (zero_detect_handler_input_all_arg_t*)argument;
    zero_detect_handler_t               zero_detect_handler = {0};
    static zero_detect_handler_private_data_t private_data = {
        .task_initialized = false,
        .task_running = false,
        .freq_valid = false,
        .callback_count = 0
    };
    
    /* 1. 检查输入参数 */
    /* 2. 挂载变量到handler，实例化handler句柄 */
    /* 2.1 实例化handler句柄 */
    /* 2.2 检查实例化结果 */
    /* 2.3 挂载handler */
    /* 3. 启动零点检测服务 */
    /* 4. 执行线程处理循环 */
    /* 5. 线程退出清理 */
}
```

### 4. Handler挂载机制

**新增挂载函数：**
```c
static void _mount_handler(zero_detect_handler_t *p_handler)
{
    ZERO_DETECT_LOG_DEBUG("mount zero_detect_handler_t");
    if (NULL == p_handler) {
        ZERO_DETECT_LOG_ERROR("mount zero_detect_handler_t failed, file: %s, line: %d",
                              __FILE__, __LINE__);
        return;
    }
    gp_zero_detect_handler = p_handler;
}
```

### 5. 实例化函数封装

**重构后的函数声明：**
```c
/* Handler实例化函数 - 设为static，不对外开放 */
static zero_detect_ret_t zero_detect_handler_inst(...);
static zero_detect_ret_t zero_detect_handler_deinst(...);
static zero_detect_ret_t zero_detect_handler_start(...);
static zero_detect_ret_t zero_detect_handler_stop(...);
```

### 6. 观察者模式强制使用

**新的对外接口实现：**
```c
zero_detect_ret_t zero_detect_register_callback(const zero_detect_callback_t *callback)
{
    /* 检查全局handler是否已挂载 */
    if (NULL == gp_zero_detect_handler) {
        ZERO_DETECT_LOG_ERROR("gp_zero_detect_handler is NULL");
        return ZERO_DETECT_ERROR_NOT_INIT;
    }
    
    /* 检查是否完成初始化 */
    if (!gp_zero_detect_handler->p_private_data->task_initialized) {
        ZERO_DETECT_LOG_ERROR("handler is not initialized");
        return ZERO_DETECT_ERROR_NOT_INIT;
    }
    
    /* 注册回调函数 */
    // ... 实现逻辑
}
```

## 架构优势

### 1. 线程内部资源管理
- ✅ **资源生命周期一致**：Handler和私有数据与线程生命周期绑定
- ✅ **内存安全**：避免了全局变量的内存管理问题
- ✅ **线程安全**：资源在线程内部创建和销毁

### 2. 封装性增强
- ✅ **实例化函数私有化**：外部无法直接访问handler实例化接口
- ✅ **数据访问控制**：移除了所有直接数据查询接口
- ✅ **接口简化**：对外接口大幅简化，只保留必要功能

### 3. 强制观察者模式
- ✅ **数据获取唯一途径**：外部只能通过回调函数获取数据
- ✅ **解耦设计**：数据生产者和消费者完全解耦
- ✅ **实时通知**：数据更新时立即通知所有订阅者

### 4. 错误处理增强
- ✅ **状态检查**：严格的初始化状态检查
- ✅ **详细日志**：包含文件名和行号的错误信息
- ✅ **健壮性**：多层次的参数和状态验证

## 删除的功能

### 1. 直接数据访问接口
- ❌ `zero_detect_handler_get_status()`
- ❌ `zero_detect_handler_get_grid_frequency()`
- ❌ `zero_detect_handler_get_signal_frequency()`

### 2. 全局访问接口
- ❌ `zero_detect_global_init()`
- ❌ `zero_detect_global_start()`
- ❌ `zero_detect_global_stop()`
- ❌ `zero_detect_global_deinit()`
- ❌ `zero_detect_get_global_status()`
- ❌ `zero_detect_get_grid_frequency()`
- ❌ `zero_detect_get_signal_frequency()`

### 3. 对外开放的Handler接口
- ❌ `zero_detect_handler_inst()` (现在为static)
- ❌ `zero_detect_handler_deinst()` (现在为static)
- ❌ `zero_detect_handler_start()` (现在为static)
- ❌ `zero_detect_handler_stop()` (现在为static)

## 使用方式变更

### 重构前的使用方式：
```c
// 1. 直接初始化
zero_detect_global_init(&input_data);
zero_detect_global_start();

// 2. 直接获取数据
float freq = zero_detect_get_grid_frequency();
zero_detect_status_t status;
zero_detect_get_global_status(&status);
```

### 重构后的使用方式：
```c
// 1. 启动线程（线程内部完成所有初始化）
osThreadNew(zero_detect_handler_thread, &input_data, &thread_attr);

// 2. 注册回调函数获取数据
zero_detect_callback_t callback = {
    .on_zero_cross = my_zero_cross_callback,
    .on_signal_freq = my_signal_freq_callback,
    .user_data = my_data
};
zero_detect_register_callback(&callback);
```

## 编译状态

### ✅ 重构成功
- 零点检测任务模块编译无错误
- 接口一致性检查通过
- 架构重构完全符合ADC模块模式

### ⚠️ 其他模块问题（非重构相关）
- 系统级别的头文件包含问题
- 其他模块的宏定义冲突
- UART接口类型不匹配问题

## 总结

成功按照ADC采集模块的架构模式完成了零点检测任务模块的重构：

1. **实现了线程内部资源管理**：Handler和私有数据在线程内部创建
2. **封装了实例化函数**：所有handler操作函数设为static
3. **强制使用观察者模式**：外部只能通过回调函数获取数据
4. **增强了错误处理**：详细的状态检查和日志记录
5. **保持了架构一致性**：与ADC模块使用相同的设计模式

重构后的架构具有更好的封装性、安全性和可维护性，同时强制外部模块使用观察者模式，实现了更好的解耦设计。
