#ifndef __ZERO_DETECT_INTEGRATION_H__
#define __ZERO_DETECT_INTEGRATION_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "zero_detect_task.h"
#include "timestamp_manager_v2.h"

/* 零点检测任务与时间戳管理器集成接口 */

/**
 * @brief 初始化零点检测任务与时间戳管理器的集成
 * @param zero_detect_config 零点检测任务配置，NULL使用默认配置
 * @param timestamp_config 时间戳管理器配置，NULL使用默认配置
 * @return zero_detect_ret_t 返回值
 */
zero_detect_ret_t zero_detect_integration_init(const zero_detect_config_t *zero_detect_config,
                                              const timestamp_manager_config_t *timestamp_config);

/**
 * @brief 启动集成系统
 * @return zero_detect_ret_t 返回值
 */
zero_detect_ret_t zero_detect_integration_start(void);

/**
 * @brief 停止集成系统
 * @return zero_detect_ret_t 返回值
 */
zero_detect_ret_t zero_detect_integration_stop(void);

/**
 * @brief 反初始化集成系统
 * @return zero_detect_ret_t 返回值
 */
zero_detect_ret_t zero_detect_integration_deinit(void);

/**
 * @brief 获取集成状态
 * @param zero_detect_status 零点检测状态输出，NULL表示不需要
 * @param timestamp_status 时间戳管理器状态输出，NULL表示不需要
 * @return zero_detect_ret_t 返回值
 */
zero_detect_ret_t zero_detect_integration_get_status(zero_detect_status_t *zero_detect_status,
                                                    timestamp_manager_status_t *timestamp_status);

/**
 * @brief 生成压缩时间戳 - 供AD7606驱动调用
 * @return uint32_t 压缩时间戳，0表示失败
 */
uint32_t zero_detect_integration_generate_packed_timestamp(void);

/**
 * @brief 计算相位修正参数
 * @param packed_timestamp 压缩时间戳
 * @param correction 输出相位修正参数
 * @return zero_detect_ret_t 返回值
 */
zero_detect_ret_t zero_detect_integration_calculate_phase_correction(uint32_t packed_timestamp,
                                                                   timestamp_phase_correction_t *correction);

/**
 * @brief 应用相位修正到FFT结果
 * @param fft_complex_data FFT复数数据
 * @param fft_length FFT长度
 * @param correction 相位修正参数
 * @return zero_detect_ret_t 返回值
 */
zero_detect_ret_t zero_detect_integration_apply_phase_correction(float *fft_complex_data,
                                                               uint16_t fft_length,
                                                               const timestamp_phase_correction_t *correction);

/**
 * @brief 打印集成状态
 */
void zero_detect_integration_print_status(void);

/* 便捷宏定义 - 简化常用操作 */

/**
 * @brief 使用默认配置初始化并启动集成系统
 */
#define ZERO_DETECT_INTEGRATION_INIT_AND_START() \
    do { \
        if (zero_detect_integration_init(NULL, NULL) == ZERO_DETECT_OK) { \
            zero_detect_integration_start(); \
        } \
    } while(0)

/**
 * @brief 停止并反初始化集成系统
 */
#define ZERO_DETECT_INTEGRATION_STOP_AND_DEINIT() \
    do { \
        zero_detect_integration_stop(); \
        zero_detect_integration_deinit(); \
    } while(0)

/**
 * @brief 获取当前电网频率（便捷接口）
 */
#define ZERO_DETECT_GET_GRID_FREQ() zero_detect_get_grid_frequency()

/**
 * @brief 获取当前信号频率（便捷接口）
 */
#define ZERO_DETECT_GET_SIGNAL_FREQ() zero_detect_get_signal_frequency()

/**
 * @brief 生成压缩时间戳（便捷接口）
 */
#define ZERO_DETECT_GENERATE_TIMESTAMP() zero_detect_integration_generate_packed_timestamp()

#ifdef __cplusplus
}
#endif

#endif /* __ZERO_DETECT_INTEGRATION_H__ */
