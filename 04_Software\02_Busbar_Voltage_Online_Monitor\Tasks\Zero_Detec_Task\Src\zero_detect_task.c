/******************************************************************************
 * @file zero_detect_task.c
 * @brief 零点检测任务实现
 * <AUTHOR> Assistant
 * @version 1.0
 * @date 2024-12-19
 *
 * @copyright Copyright (c) 2024
 *
 * Processing flow:
 * 1. 初始化零点检测任务和观察者管理
 * 2. 处理TIM4零点捕获中断事件
 * 3. 处理TIM3信号频率检测事件
 * 4. 计算和更新电网频率
 * 5. 通过观察者模式通知订阅者
 *
 * @par dependencies
 * - FreeRTOS
 * - TIM3/TIM4 硬件定时器
 *
 * @note 1 tab == 4 spaces!
 *
 *****************************************************************************/

/* ==================== 头文件包含 ==================== */
#include "zero_detect_task.h"
#include <string.h>
#include <stdio.h>

/* ==================== 私有宏定义 ==================== */
#define ZERO_DETECT_TASK_NOTIFICATION_ZERO_CROSS   (1UL << 0)
#define ZERO_DETECT_TASK_NOTIFICATION_SIGNAL_FREQ  (1UL << 1)
#define ZERO_DETECT_TASK_NOTIFICATION_OVERFLOW     (1UL << 2)
#define ZERO_DETECT_TASK_NOTIFICATION_ERROR        (1UL << 3)
#define ZERO_DETECT_TASK_NOTIFICATION_STOP         (1UL << 4)

#define ZERO_DETECT_MAX_OBSERVERS                  8
#define ZERO_DETECT_TASK_WAIT_TIMEOUT              portMAX_DELAY

/* ==================== 私有类型定义 ==================== */
/* 内部事件数据结构 */
typedef struct {
    uint16_t capture_count;
    uint16_t t_zero;
    float frequency;
} zero_detect_zero_cross_data_t;

typedef struct {
    float frequency;
} zero_detect_signal_freq_data_t;

typedef struct {
    uint16_t old_capture_count;
} zero_detect_overflow_data_t;

typedef struct {
    int32_t error_code;
    const char *error_msg;
} zero_detect_error_data_t;

/* ==================== 私有数据结构定义 ==================== */

/*                 zero_detect_handler私有数据结构                            */
struct zero_detect_handler_private_data_t
{
    /* 任务运行状态 */
    bool                    task_running;               /* 任务运行标志 */
    bool                    task_initialized;          /* 任务初始化标志 */
    
    /* 频率计算相关 */
    uint32_t               freq_calc_cycle_count;      /* 频率计算周期计数 */
    uint32_t               last_zero_cross_time;       /* 上次零点时间 */
    
    /* 统计信息 */
    uint32_t               total_zero_cross_count;     /* 总零点捕获次数 */
    uint32_t               total_overflow_count;       /* 总溢出次数 */
    uint32_t               freq_update_count;          /* 频率更新次数 */
    
    /* 当前状态 */
    float                  current_grid_freq;          /* 当前电网频率 */
    float                  current_signal_freq;        /* 当前信号频率 */
    bool                   freq_valid;                 /* 频率有效标志 */
    
    /* 捕获数据 */
    uint16_t               current_capture_count;      /* 当前捕获计数 */
    uint16_t               current_t_zero;             /* 当前零点时间戳 */
    
    /* 观察者模式回调 */
    zero_detect_callback_t callbacks[ZERO_DETECT_MAX_CALLBACKS];
    uint8_t                callback_count;             /* 回调函数数量 */
};

/* ==================== 全局变量定义 ==================== */

/* 全局零点检测句柄 */
static zero_detect_handler_t g_zero_detect_handler = {0};
static bool g_zero_detect_global_initialized = false;

/* 事件数据缓冲区 */
static zero_detect_zero_cross_data_t g_zero_cross_data = {0};
static zero_detect_signal_freq_data_t g_signal_freq_data = {0};
static zero_detect_overflow_data_t g_overflow_data = {0};
static zero_detect_error_data_t g_error_data = {0};

/* ==================== 私有函数声明 ==================== */

static zero_detect_ret_t zero_detect_validate_input_params(zero_detect_handler_input_all_arg_t *p_input_data);
static zero_detect_ret_t zero_detect_init_private_data(zero_detect_handler_t *p_handler);
static zero_detect_ret_t zero_detect_create_rtos_resources(zero_detect_handler_t *p_handler);
static zero_detect_ret_t zero_detect_destroy_rtos_resources(zero_detect_handler_t *p_handler);
static zero_detect_ret_t zero_detect_start_capture_timers(zero_detect_handler_t *p_handler);
static zero_detect_ret_t zero_detect_stop_capture_timers(zero_detect_handler_t *p_handler);
static bool zero_detect_is_frequency_valid(float frequency);
static void zero_detect_notify_observers_zero_cross(zero_detect_handler_t *p_handler, 
                                                   uint16_t capture_count, uint16_t t_zero, float frequency);
static void zero_detect_notify_observers_signal_freq(zero_detect_handler_t *p_handler, float frequency);
static void zero_detect_notify_observers_overflow(zero_detect_handler_t *p_handler, uint16_t old_capture_count);
static void zero_detect_notify_observers_error(zero_detect_handler_t *p_handler, 
                                              zero_detect_ret_t error_code, const char *error_msg);

/* ==================== 主要接口函数实现 ==================== */

/******************************************************************************
 * @brief 零点检测处理器实例化
 *
 * @param  p_handler               零点检测句柄指针
 * @param  p_input_data            输入参数结构体指针
 *
 * @return zero_detect_ret_t       返回值
 *****************************************************************************/
zero_detect_ret_t zero_detect_handler_inst(zero_detect_handler_t *p_handler,
                                          zero_detect_handler_input_all_arg_t *p_input_data)
{
    if (p_handler == NULL || p_input_data == NULL) {
        ZERO_DETECT_LOG_ERROR("Invalid parameters");
        return ZERO_DETECT_ERROR_PARAM;
    }

    /* 验证输入参数 */
    if (zero_detect_validate_input_params(p_input_data) != ZERO_DETECT_OK) {
        ZERO_DETECT_LOG_ERROR("Input parameters validation failed");
        return ZERO_DETECT_ERROR_PARAM;
    }

    /* 检查是否已经初始化 */
    if (p_handler->p_private_data != NULL) {
        ZERO_DETECT_LOG_ERROR("Handler already initialized");
        return ZERO_DETECT_ERROR_ALREADY_INIT;
    }

    /* 分配私有数据内存 */
    p_handler->p_private_data = pvPortMalloc(sizeof(zero_detect_handler_private_data_t));
    if (p_handler->p_private_data == NULL) {
        ZERO_DETECT_LOG_ERROR("Failed to allocate private data memory");
        return ZERO_DETECT_ERROR_MEMORY;
    }

    /* 初始化私有数据 */
    if (zero_detect_init_private_data(p_handler) != ZERO_DETECT_OK) {
        vPortFree(p_handler->p_private_data);
        p_handler->p_private_data = NULL;
        return ZERO_DETECT_ERROR;
    }

    /* 保存接口指针 */
    p_handler->p_system_timebase_interface = p_input_data->p_system_timebase_interface;
    p_handler->p_system_interrupt_interface = p_input_data->p_system_interrupt_interface;
    p_handler->p_zero_detect_callback_fun = p_input_data->p_zero_detect_callback_fun;
    p_handler->p_zero_detect_timer_control = p_input_data->p_zero_detect_timer_control;
    p_handler->p_zero_detect_handler_os_interface = p_input_data->p_zero_detect_handler_os_interface;
    p_handler->p_system_config = p_input_data->p_system_config;

    /* 创建RTOS资源 */
    if (zero_detect_create_rtos_resources(p_handler) != ZERO_DETECT_OK) {
        vPortFree(p_handler->p_private_data);
        p_handler->p_private_data = NULL;
        return ZERO_DETECT_ERROR;
    }

    /* 初始化中断接口 */
    if (p_handler->p_system_interrupt_interface->pfinit() != 0) {
        ZERO_DETECT_LOG_ERROR("Failed to initialize interrupt interface");
        zero_detect_destroy_rtos_resources(p_handler);
        vPortFree(p_handler->p_private_data);
        p_handler->p_private_data = NULL;
        return ZERO_DETECT_ERROR;
    }

    /* 创建零点检测任务 */
    if (p_handler->p_zero_detect_handler_os_interface->rtos_task_create(
            zero_detect_handler_thread,
            "ZeroDetectTask",
            p_handler->p_system_config->zero_detect_task_stack_size,
            p_handler,
            p_handler->p_system_config->zero_detect_task_priority,
            &p_handler->zero_detect_task_thread) != ZERO_DETECT_RTOS_PDTRUE) {
        ZERO_DETECT_LOG_ERROR("Failed to create zero detect task");
        zero_detect_destroy_rtos_resources(p_handler);
        vPortFree(p_handler->p_private_data);
        p_handler->p_private_data = NULL;
        return ZERO_DETECT_ERROR;
    }

    p_handler->p_private_data->task_initialized = true;
    ZERO_DETECT_LOG_INFO("Zero detect handler initialized successfully");
    
    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 零点检测处理器反实例化
 *
 * @param  p_handler               零点检测句柄指针
 *
 * @return zero_detect_ret_t       返回值
 *****************************************************************************/
zero_detect_ret_t zero_detect_handler_deinst(zero_detect_handler_t *p_handler)
{
    if (p_handler == NULL || p_handler->p_private_data == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    /* 停止任务 */
    if (p_handler->p_private_data->task_running) {
        zero_detect_handler_stop(p_handler);
    }

    /* 发送停止通知并等待任务结束 */
    if (p_handler->zero_detect_task_thread != NULL) {
        p_handler->p_zero_detect_handler_os_interface->rtos_task_notify(
            p_handler->zero_detect_task_thread,
            ZERO_DETECT_TASK_NOTIFICATION_STOP,
            2); // eSetBits
        
        /* 等待任务结束 */
        p_handler->p_zero_detect_handler_os_interface->rtos_delay(100);
        
        /* 删除任务 */
        p_handler->p_zero_detect_handler_os_interface->rtos_task_delete(p_handler->zero_detect_task_thread);
        p_handler->zero_detect_task_thread = NULL;
    }

    /* 反初始化中断接口 */
    if (p_handler->p_system_interrupt_interface != NULL) {
        p_handler->p_system_interrupt_interface->pfdeinit();
    }

    /* 销毁RTOS资源 */
    zero_detect_destroy_rtos_resources(p_handler);

    /* 释放私有数据内存 */
    vPortFree(p_handler->p_private_data);
    p_handler->p_private_data = NULL;

    /* 清空接口指针 */
    memset(p_handler, 0, sizeof(zero_detect_handler_t));

    ZERO_DETECT_LOG_INFO("Zero detect handler deinitialized");
    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 零点检测处理器启动
 *
 * @param  p_handler               零点检测句柄指针
 *
 * @return zero_detect_ret_t       返回值
 *****************************************************************************/
zero_detect_ret_t zero_detect_handler_start(zero_detect_handler_t *p_handler)
{
    if (p_handler == NULL || p_handler->p_private_data == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    if (!p_handler->p_private_data->task_initialized) {
        ZERO_DETECT_LOG_ERROR("Handler not initialized");
        return ZERO_DETECT_ERROR_NOT_INIT;
    }

    if (p_handler->p_private_data->task_running) {
        ZERO_DETECT_LOG_WARNING("Handler already running");
        return ZERO_DETECT_OK;
    }

    /* 使能中断 */
    if (p_handler->p_system_interrupt_interface->pfenable_irq() != 0) {
        ZERO_DETECT_LOG_ERROR("Failed to enable interrupt");
        return ZERO_DETECT_ERROR;
    }

    /* 启动捕获定时器 */
    if (zero_detect_start_capture_timers(p_handler) != ZERO_DETECT_OK) {
        ZERO_DETECT_LOG_ERROR("Failed to start capture timers");
        p_handler->p_system_interrupt_interface->pfdisable_irq();
        return ZERO_DETECT_ERROR;
    }

    /* 重置统计信息 */
    p_handler->p_private_data->freq_calc_cycle_count = 0;
    p_handler->p_private_data->last_zero_cross_time = 0;
    p_handler->p_private_data->current_grid_freq = 0.0f;
    p_handler->p_private_data->current_signal_freq = 0.0f;
    p_handler->p_private_data->freq_valid = false;

    p_handler->p_private_data->task_running = true;
    ZERO_DETECT_LOG_INFO("Zero detect handler started");
    
    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 零点检测处理器停止
 *
 * @param  p_handler               零点检测句柄指针
 *
 * @return zero_detect_ret_t       返回值
 *****************************************************************************/
zero_detect_ret_t zero_detect_handler_stop(zero_detect_handler_t *p_handler)
{
    if (p_handler == NULL || p_handler->p_private_data == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    if (!p_handler->p_private_data->task_running) {
        return ZERO_DETECT_OK;
    }

    /* 停止捕获定时器 */
    zero_detect_stop_capture_timers(p_handler);

    /* 禁用中断 */
    if (p_handler->p_system_interrupt_interface->pfdisable_irq() != 0) {
        ZERO_DETECT_LOG_ERROR("Failed to disable interrupt");
    }

    p_handler->p_private_data->task_running = false;
    ZERO_DETECT_LOG_INFO("Zero detect handler stopped");
    
    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 零点检测处理器线程
 *
 * @param  arg                     线程参数
 *
 * @return void
 *****************************************************************************/
void zero_detect_handler_thread(void *arg)
{
    zero_detect_handler_t *p_handler = (zero_detect_handler_t *)arg;
    uint32_t notification_value;

    if (p_handler == NULL || p_handler->p_private_data == NULL) {
        ZERO_DETECT_LOG_ERROR("Invalid handler in thread");
        return;
    }

    ZERO_DETECT_LOG_INFO("Zero detect handler thread started");

    while (1) {
        /* 等待任务通知 */
        if (p_handler->p_zero_detect_handler_os_interface->rtos_task_notify_wait(
                0, ULONG_MAX, &notification_value, ZERO_DETECT_TASK_WAIT_TIMEOUT) == ZERO_DETECT_RTOS_PDTRUE) {
            
            /* 处理零点捕获事件 */
            if (notification_value & ZERO_DETECT_TASK_NOTIFICATION_ZERO_CROSS) {
                zero_detect_process_zero_cross_handler(p_handler, &g_zero_cross_data);
            }

            /* 处理信号频率事件 */
            if (notification_value & ZERO_DETECT_TASK_NOTIFICATION_SIGNAL_FREQ) {
                zero_detect_process_signal_freq_handler(p_handler, &g_signal_freq_data);
            }

            /* 处理溢出事件 */
            if (notification_value & ZERO_DETECT_TASK_NOTIFICATION_OVERFLOW) {
                zero_detect_process_overflow_handler(p_handler, &g_overflow_data);
            }

            /* 处理错误事件 */
            if (notification_value & ZERO_DETECT_TASK_NOTIFICATION_ERROR) {
                zero_detect_process_error_handler(p_handler, &g_error_data);
            }

            /* 处理停止事件 */
            if (notification_value & ZERO_DETECT_TASK_NOTIFICATION_STOP) {
                ZERO_DETECT_LOG_INFO("Received stop notification, exiting thread");
                break;
            }
        }
    }

    ZERO_DETECT_LOG_INFO("Zero detect handler thread exited");
    p_handler->p_zero_detect_handler_os_interface->rtos_task_delete(NULL);
}

/* ==================== 私有函数实现 ==================== */

/******************************************************************************
 * @brief 验证输入参数
 *****************************************************************************/
static zero_detect_ret_t zero_detect_validate_input_params(zero_detect_handler_input_all_arg_t *p_input_data)
{
    if (p_input_data->p_system_config == NULL ||
        p_input_data->p_system_timebase_interface == NULL ||
        p_input_data->p_system_interrupt_interface == NULL ||
        p_input_data->p_zero_detect_callback_fun == NULL ||
        p_input_data->p_zero_detect_timer_control == NULL ||
        p_input_data->p_zero_detect_handler_os_interface == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    /* 验证系统配置参数 */
    if (p_input_data->p_system_config->zero_detect_task_stack_size < 256 ||
        p_input_data->p_system_config->zero_detect_task_priority == 0 ||
        p_input_data->p_system_config->freq_update_cycles == 0) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 初始化私有数据
 *****************************************************************************/
static zero_detect_ret_t zero_detect_init_private_data(zero_detect_handler_t *p_handler)
{
    memset(p_handler->p_private_data, 0, sizeof(zero_detect_handler_private_data_t));
    
    p_handler->p_private_data->task_running = false;
    p_handler->p_private_data->task_initialized = false;
    p_handler->p_private_data->freq_valid = false;
    p_handler->p_private_data->callback_count = 0;
    
    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 创建RTOS资源
 *****************************************************************************/
static zero_detect_ret_t zero_detect_create_rtos_resources(zero_detect_handler_t *p_handler)
{
    /* 创建互斥锁 */
    if (p_handler->p_zero_detect_handler_os_interface->rtos_mutex_create(&p_handler->zero_detect_mutex) 
        != ZERO_DETECT_RTOS_PDTRUE) {
        ZERO_DETECT_LOG_ERROR("Failed to create mutex");
        return ZERO_DETECT_ERROR;
    }

    /* 创建信号量 */
    if (p_handler->p_zero_detect_handler_os_interface->rtos_semphorebinary_create(&p_handler->zero_detect_semphore) 
        != ZERO_DETECT_RTOS_PDTRUE) {
        ZERO_DETECT_LOG_ERROR("Failed to create semaphore");
        p_handler->p_zero_detect_handler_os_interface->rtos_mutex_delete(p_handler->zero_detect_mutex);
        return ZERO_DETECT_ERROR;
    }

    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 销毁RTOS资源
 *****************************************************************************/
static zero_detect_ret_t zero_detect_destroy_rtos_resources(zero_detect_handler_t *p_handler)
{
    if (p_handler->zero_detect_mutex != NULL) {
        p_handler->p_zero_detect_handler_os_interface->rtos_mutex_delete(p_handler->zero_detect_mutex);
        p_handler->zero_detect_mutex = NULL;
    }

    if (p_handler->zero_detect_semphore != NULL) {
        p_handler->p_zero_detect_handler_os_interface->rtos_semphore_delete(p_handler->zero_detect_semphore);
        p_handler->zero_detect_semphore = NULL;
    }

    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 启动捕获定时器
 *****************************************************************************/
static zero_detect_ret_t zero_detect_start_capture_timers(zero_detect_handler_t *p_handler)
{
    /* 重置捕获计数器 */
    if (p_handler->p_zero_detect_timer_control->pfreset_capture_count() != ZERO_DETECT_RTOS_PDTRUE) {
        ZERO_DETECT_LOG_ERROR("Failed to reset capture count");
        return ZERO_DETECT_ERROR;
    }

    /* 启动TIM4零点捕获 */
    if (p_handler->p_zero_detect_timer_control->pfstart_tim4_capture() != ZERO_DETECT_RTOS_PDTRUE) {
        ZERO_DETECT_LOG_ERROR("Failed to start TIM4 capture");
        return ZERO_DETECT_ERROR;
    }

    /* 启动TIM3信号频率捕获 */
    if (p_handler->p_zero_detect_timer_control->pfstart_tim3_capture() != ZERO_DETECT_RTOS_PDTRUE) {
        ZERO_DETECT_LOG_ERROR("Failed to start TIM3 capture");
        p_handler->p_zero_detect_timer_control->pfstop_tim4_capture();
        return ZERO_DETECT_ERROR;
    }

    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 停止捕获定时器
 *****************************************************************************/
static zero_detect_ret_t zero_detect_stop_capture_timers(zero_detect_handler_t *p_handler)
{
    p_handler->p_zero_detect_timer_control->pfstop_tim4_capture();
    p_handler->p_zero_detect_timer_control->pfstop_tim3_capture();
    return ZERO_DETECT_OK;
}

/* ==================== 事件处理函数实现 ==================== */

/******************************************************************************
 * @brief 处理零点捕获事件（Handler版本）
 *****************************************************************************/
static zero_detect_ret_t zero_detect_process_zero_cross_handler(zero_detect_handler_t *p_handler,
                                                               const zero_detect_zero_cross_data_t *data)
{
    if (p_handler == NULL || data == NULL || p_handler->p_private_data == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    ZERO_DETECT_LOG_DEBUG("Zero cross: count=%u, t_zero=%u, freq=%.2f",
                          data->capture_count, data->t_zero, data->frequency);

    /* 获取互斥锁 */
    p_handler->p_zero_detect_handler_os_interface->rtos_mutex_take(p_handler->zero_detect_mutex, portMAX_DELAY);

    /* 更新私有数据 */
    p_handler->p_private_data->current_capture_count = data->capture_count;
    p_handler->p_private_data->current_t_zero = data->t_zero;
    p_handler->p_private_data->total_zero_cross_count++;

    /* 验证频率有效性 */
    if (zero_detect_is_frequency_valid(data->frequency)) {
        p_handler->p_private_data->current_grid_freq = data->frequency;
        p_handler->p_private_data->freq_valid = true;
        p_handler->p_private_data->freq_calc_cycle_count++;

        /* 按配置的周期数更新频率 */
        if (p_handler->p_private_data->freq_calc_cycle_count >= p_handler->p_system_config->freq_update_cycles) {
            p_handler->p_private_data->freq_update_count++;
            p_handler->p_private_data->freq_calc_cycle_count = 0;
            ZERO_DETECT_LOG_INFO("Grid frequency updated: %.2f Hz", data->frequency);
        }
    } else {
        p_handler->p_private_data->freq_valid = false;
        ZERO_DETECT_LOG_ERROR("Invalid grid frequency: %.2f Hz", data->frequency);
    }

    /* 检查溢出 */
    if (data->capture_count >= p_handler->p_system_config->capture_overflow_threshold) {
        ZERO_DETECT_LOG_ERROR("Capture count approaching overflow: %u", data->capture_count);
        p_handler->p_private_data->total_overflow_count++;
    }

    /* 释放互斥锁 */
    p_handler->p_zero_detect_handler_os_interface->rtos_mutex_give(p_handler->zero_detect_mutex);

    /* 通知观察者 */
    zero_detect_notify_observers_zero_cross(p_handler, data->capture_count, data->t_zero, data->frequency);

    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 处理信号频率事件（Handler版本）
 *****************************************************************************/
static zero_detect_ret_t zero_detect_process_signal_freq_handler(zero_detect_handler_t *p_handler,
                                                                const zero_detect_signal_freq_data_t *data)
{
    if (p_handler == NULL || data == NULL || p_handler->p_private_data == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    ZERO_DETECT_LOG_DEBUG("Signal frequency: %.2f Hz", data->frequency);

    p_handler->p_zero_detect_handler_os_interface->rtos_mutex_take(p_handler->zero_detect_mutex, portMAX_DELAY);
    if (zero_detect_is_frequency_valid(data->frequency)) {
        p_handler->p_private_data->current_signal_freq = data->frequency;
        ZERO_DETECT_LOG_DEBUG("Signal frequency updated: %.2f Hz", data->frequency);
    } else {
        ZERO_DETECT_LOG_ERROR("Invalid signal frequency: %.2f Hz", data->frequency);
    }
    p_handler->p_zero_detect_handler_os_interface->rtos_mutex_give(p_handler->zero_detect_mutex);

    /* 通知观察者 */
    zero_detect_notify_observers_signal_freq(p_handler, data->frequency);

    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 处理溢出事件（Handler版本）
 *****************************************************************************/
static zero_detect_ret_t zero_detect_process_overflow_handler(zero_detect_handler_t *p_handler,
                                                             const zero_detect_overflow_data_t *data)
{
    if (p_handler == NULL || data == NULL || p_handler->p_private_data == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    ZERO_DETECT_LOG_ERROR("Overflow detected, old_count: %u", data->old_capture_count);

    p_handler->p_zero_detect_handler_os_interface->rtos_mutex_take(p_handler->zero_detect_mutex, portMAX_DELAY);
    p_handler->p_private_data->total_overflow_count++;
    /* 重置相关计数器 */
    p_handler->p_private_data->freq_calc_cycle_count = 0;
    p_handler->p_private_data->last_zero_cross_time = 0;
    p_handler->p_zero_detect_handler_os_interface->rtos_mutex_give(p_handler->zero_detect_mutex);

    /* 通知观察者 */
    zero_detect_notify_observers_overflow(p_handler, data->old_capture_count);

    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 处理错误事件（Handler版本）
 *****************************************************************************/
static zero_detect_ret_t zero_detect_process_error_handler(zero_detect_handler_t *p_handler,
                                                          const zero_detect_error_data_t *data)
{
    if (p_handler == NULL || data == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    ZERO_DETECT_LOG_ERROR("Error event: code=%d, msg=%s", data->error_code,
                          data->error_msg ? data->error_msg : "NULL");

    /* 通知观察者 */
    zero_detect_notify_observers_error(p_handler, (zero_detect_ret_t)data->error_code, data->error_msg);

    return ZERO_DETECT_OK;
}

/* ==================== 观察者模式实现 ==================== */

/******************************************************************************
 * @brief 注册回调函数
 *****************************************************************************/
zero_detect_ret_t zero_detect_handler_register_callback(zero_detect_handler_t *p_handler,
                                                       const zero_detect_callback_t *callback)
{
    if (p_handler == NULL || callback == NULL || p_handler->p_private_data == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    p_handler->p_zero_detect_handler_os_interface->rtos_mutex_take(p_handler->zero_detect_mutex, portMAX_DELAY);
    
    if (p_handler->p_private_data->callback_count >= ZERO_DETECT_MAX_CALLBACKS) {
        p_handler->p_zero_detect_handler_os_interface->rtos_mutex_give(p_handler->zero_detect_mutex);
        return ZERO_DETECT_ERROR;
    }

    p_handler->p_private_data->callbacks[p_handler->p_private_data->callback_count] = *callback;
    p_handler->p_private_data->callback_count++;
    
    p_handler->p_zero_detect_handler_os_interface->rtos_mutex_give(p_handler->zero_detect_mutex);
    
    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 获取当前状态
 *****************************************************************************/
zero_detect_ret_t zero_detect_handler_get_status(zero_detect_handler_t *p_handler,
                                                zero_detect_status_t *status)
{
    if (p_handler == NULL || status == NULL || p_handler->p_private_data == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    p_handler->p_zero_detect_handler_os_interface->rtos_mutex_take(p_handler->zero_detect_mutex, portMAX_DELAY);
    
    status->initialized = p_handler->p_private_data->task_initialized;
    status->running = p_handler->p_private_data->task_running;
    status->capture_count = p_handler->p_private_data->current_capture_count;
    status->t_zero = p_handler->p_private_data->current_t_zero;
    status->grid_freq_hz = p_handler->p_private_data->current_grid_freq;
    status->signal_freq_hz = p_handler->p_private_data->current_signal_freq;
    status->freq_valid = p_handler->p_private_data->freq_valid;
    status->total_zero_cross_count = p_handler->p_private_data->total_zero_cross_count;
    status->overflow_count = p_handler->p_private_data->total_overflow_count;
    status->freq_update_count = p_handler->p_private_data->freq_update_count;
    
    p_handler->p_zero_detect_handler_os_interface->rtos_mutex_give(p_handler->zero_detect_mutex);
    
    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 获取电网频率
 *****************************************************************************/
zero_detect_ret_t zero_detect_handler_get_grid_frequency(zero_detect_handler_t *p_handler, float *frequency)
{
    if (p_handler == NULL || frequency == NULL || p_handler->p_private_data == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    p_handler->p_zero_detect_handler_os_interface->rtos_mutex_take(p_handler->zero_detect_mutex, portMAX_DELAY);
    *frequency = p_handler->p_private_data->current_grid_freq;
    p_handler->p_zero_detect_handler_os_interface->rtos_mutex_give(p_handler->zero_detect_mutex);
    
    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 获取信号频率
 *****************************************************************************/
zero_detect_ret_t zero_detect_handler_get_signal_frequency(zero_detect_handler_t *p_handler, float *frequency)
{
    if (p_handler == NULL || frequency == NULL || p_handler->p_private_data == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    p_handler->p_zero_detect_handler_os_interface->rtos_mutex_take(p_handler->zero_detect_mutex, portMAX_DELAY);
    *frequency = p_handler->p_private_data->current_signal_freq;
    p_handler->p_zero_detect_handler_os_interface->rtos_mutex_give(p_handler->zero_detect_mutex);
    
    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 生成压缩时间戳
 *
 * @param  handle 零点检测句柄
 *
 * @return uint32_t 压缩时间戳，0表示失败
 *****************************************************************************/
uint32_t zero_detect_task_generate_packed_timestamp(zero_detect_handle_t *handle)
{
    if (handle == NULL || !handle->initialized) {
        return 0;
    }

    uint32_t timestamp = 0;
    xSemaphoreTake(handle->mutex, portMAX_DELAY);
    
    /* 获取当前定时器计数值作为t_sample0 */
    uint16_t t_sample0 = (uint16_t)xTaskGetTickCount(); // 简化实现，实际应获取定时器计数
    timestamp = PACK_TIMESTAMP(handle->status.capture_count, t_sample0);
    
    xSemaphoreGive(handle->mutex);

    return timestamp;
}

/* ==================== 中断回调接口实现 ==================== */

/******************************************************************************
 * @brief TIM4零点捕获中断回调
 *
 * @param  capture_count 捕获计数
 * @param  t_zero 零点时间戳
 * @param  frequency 频率值
 * @param  user_data 用户数据
 *
 * @return void
 *****************************************************************************/
void zero_detect_task_tim4_callback(uint16_t capture_count, uint16_t t_zero,
                                   float frequency, void *user_data)
{
    zero_detect_handle_t *handle = (zero_detect_handle_t *)user_data;
    if (handle == NULL || !handle->initialized || !handle->running) {
        return;
    }

    /* 存储事件数据 */
    g_zero_cross_data.capture_count = capture_count;
    g_zero_cross_data.t_zero = t_zero;
    g_zero_cross_data.frequency = frequency;

    /* 发送任务通知 */
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    xTaskNotifyFromISR(handle->task_handle, ZERO_DETECT_TASK_NOTIFICATION_ZERO_CROSS,
                       eSetBits, &xHigherPriorityTaskWoken);
    portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
}

/******************************************************************************
 * @brief TIM3信号频率中断回调
 *
 * @param  frequency 信号频率
 * @param  user_data 用户数据
 *
 * @return void
 *****************************************************************************/
void zero_detect_task_tim3_callback(float frequency, void *user_data)
{
    zero_detect_handle_t *handle = (zero_detect_handle_t *)user_data;
    if (handle == NULL || !handle->initialized || !handle->running) {
        return;
    }

    /* 存储事件数据 */
    g_signal_freq_data.frequency = frequency;

    /* 发送任务通知 */
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    xTaskNotifyFromISR(handle->task_handle, ZERO_DETECT_TASK_NOTIFICATION_SIGNAL_FREQ,
                       eSetBits, &xHigherPriorityTaskWoken);
    portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
}

/******************************************************************************
 * @brief 溢出处理回调
 *
 * @param  old_capture_count 旧的捕获计数
 * @param  user_data 用户数据
 *
 * @return void
 *****************************************************************************/
void zero_detect_task_overflow_callback(uint16_t old_capture_count, void *user_data)
{
    zero_detect_handle_t *handle = (zero_detect_handle_t *)user_data;
    if (handle == NULL || !handle->initialized || !handle->running) {
        return;
    }

    /* 存储事件数据 */
    g_overflow_data.old_capture_count = old_capture_count;

    /* 发送任务通知 */
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    xTaskNotifyFromISR(handle->task_handle, ZERO_DETECT_TASK_NOTIFICATION_OVERFLOW,
                       eSetBits, &xHigherPriorityTaskWoken);
    portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
}

/******************************************************************************
 * @brief 错误处理回调
 *
 * @param  error_code 错误代码
 * @param  error_msg 错误消息
 * @param  user_data 用户数据
 *
 * @return void
 *****************************************************************************/
void zero_detect_task_error_callback(int32_t error_code, const char *error_msg, void *user_data)
{
    zero_detect_handle_t *handle = (zero_detect_handle_t *)user_data;
    if (handle == NULL || !handle->initialized || !handle->running) {
        return;
    }

    /* 存储事件数据 */
    g_error_data.error_code = error_code;
    g_error_data.error_msg = error_msg;

    /* 发送任务通知 */
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    xTaskNotifyFromISR(handle->task_handle, ZERO_DETECT_TASK_NOTIFICATION_ERROR,
                       eSetBits, &xHigherPriorityTaskWoken);
    portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
}

/* ==================== 工具函数实现 ==================== */

/******************************************************************************
 * @brief 获取默认配置
 *
 * @return zero_detect_config_t
 *****************************************************************************/
zero_detect_config_t zero_detect_get_default_config(void)
{
    zero_detect_config_t config = {
        .task_stack_size = ZERO_DETECT_TASK_STACK_SIZE,
        .task_priority = ZERO_DETECT_TASK_PRIORITY,
        .freq_update_cycles = ZERO_DETECT_FREQ_UPDATE_CYCLES,
        .min_valid_freq_hz = ZERO_DETECT_MIN_VALID_FREQ_HZ,
        .max_valid_freq_hz = ZERO_DETECT_MAX_VALID_FREQ_HZ,
        .capture_overflow_threshold = ZERO_DETECT_CAPTURE_OVERFLOW_THRESHOLD,
    };
    return config;
}

/******************************************************************************
 * @brief 获取错误字符串
 *
 * @param  error 错误码
 *
 * @return const char*
 *****************************************************************************/
const char* zero_detect_get_error_string(zero_detect_ret_t error)
{
    switch (error) {
        case ZERO_DETECT_OK: return "Success";
        case ZERO_DETECT_ERROR: return "General error";
        case ZERO_DETECT_ERROR_PARAM: return "Parameter error";
        case ZERO_DETECT_ERROR_NOT_INIT: return "Not initialized";
        case ZERO_DETECT_ERROR_ALREADY_INIT: return "Already initialized";
        case ZERO_DETECT_ERROR_OVERFLOW: return "Overflow error";
        case ZERO_DETECT_ERROR_TIMEOUT: return "Timeout error";
        case ZERO_DETECT_ERROR_MEMORY: return "Memory error";
        default: return "Unknown error";
    }
}

/* ==================== 全局访问接口实现 ==================== */

/******************************************************************************
 * @brief 全局初始化零点检测任务
 *
 * @param  config 配置参数
 *
 * @return zero_detect_ret_t
 *****************************************************************************/
zero_detect_ret_t zero_detect_global_init(const zero_detect_config_t *config)
{
    if (g_zero_detect_global_initialized) {
        ZERO_DETECT_LOG_ERROR("Global task already initialized");
        return ZERO_DETECT_ERROR_ALREADY_INIT;
    }

    zero_detect_ret_t ret = zero_detect_task_init(&g_zero_detect_handle, config);
    if (ret != ZERO_DETECT_OK) {
        return ret;
    }

    g_zero_detect_global_initialized = true;
    ZERO_DETECT_LOG_INFO("Global task initialized");
    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 全局启动零点检测任务
 *
 * @return zero_detect_ret_t
 *****************************************************************************/
zero_detect_ret_t zero_detect_global_start(void)
{
    if (!g_zero_detect_global_initialized) {
        return ZERO_DETECT_ERROR_NOT_INIT;
    }

    return zero_detect_task_start(&g_zero_detect_handle);
}

/******************************************************************************
 * @brief 全局停止零点检测任务
 *
 * @return zero_detect_ret_t
 *****************************************************************************/
zero_detect_ret_t zero_detect_global_stop(void)
{
    if (!g_zero_detect_global_initialized) {
        return ZERO_DETECT_ERROR_NOT_INIT;
    }

    return zero_detect_task_stop(&g_zero_detect_handle);
}

/******************************************************************************
 * @brief 全局反初始化零点检测任务
 *
 * @return zero_detect_ret_t
 *****************************************************************************/
zero_detect_ret_t zero_detect_global_deinit(void)
{
    if (!g_zero_detect_global_initialized) {
        return ZERO_DETECT_ERROR_NOT_INIT;
    }

    zero_detect_ret_t ret = zero_detect_task_deinit(&g_zero_detect_handle);
    if (ret == ZERO_DETECT_OK) {
        g_zero_detect_global_initialized = false;
        ZERO_DETECT_LOG_INFO("Global task deinitialized");
    }

    return ret;
}

/******************************************************************************
 * @brief 获取全局零点检测状态
 *
 * @param  status 状态结构体指针
 *
 * @return zero_detect_ret_t
 *****************************************************************************/
zero_detect_ret_t zero_detect_get_global_status(zero_detect_status_t *status)
{
    if (!g_zero_detect_global_initialized) {
        return ZERO_DETECT_ERROR_NOT_INIT;
    }

    return zero_detect_task_get_status(&g_zero_detect_handle, status);
}

/******************************************************************************
 * @brief 获取当前电网频率
 *
 * @return float
 *****************************************************************************/
float zero_detect_get_grid_frequency(void)
{
    if (!g_zero_detect_global_initialized) {
        return 0.0f;
    }

    float frequency = 0.0f;
    zero_detect_task_get_grid_frequency(&g_zero_detect_handle, &frequency);
    return frequency;
}

/******************************************************************************
 * @brief 获取当前信号频率
 *
 * @return float
 *****************************************************************************/
float zero_detect_get_signal_frequency(void)
{
    if (!g_zero_detect_global_initialized) {
        return 0.0f;
    }

    float frequency = 0.0f;
    zero_detect_task_get_signal_frequency(&g_zero_detect_handle, &frequency);
    return frequency;
}

/* ==================== 私有函数实现 ==================== */

/******************************************************************************
 * @brief 零点检测任务入口函数
 *
 * @param  pvParameters 任务参数
 *
 * @return void
 *****************************************************************************/
static void zero_detect_task_entry(void *pvParameters)
{
    zero_detect_handle_t *handle = (zero_detect_handle_t *)pvParameters;
    uint32_t notification_value;

    ZERO_DETECT_LOG_INFO("ZeroDetectTask started");

    while (1) {
        /* 等待通知 */
        if (xTaskNotifyWait(0, ULONG_MAX, &notification_value, ZERO_DETECT_TASK_WAIT_TIMEOUT) == pdTRUE) {
            
            /* 处理零点捕获事件 */
            if (notification_value & ZERO_DETECT_TASK_NOTIFICATION_ZERO_CROSS) {
                zero_detect_process_zero_cross(handle, &g_zero_cross_data);
            }

            /* 处理信号频率事件 */
            if (notification_value & ZERO_DETECT_TASK_NOTIFICATION_SIGNAL_FREQ) {
                zero_detect_process_signal_freq(handle, &g_signal_freq_data);
            }

            /* 处理溢出事件 */
            if (notification_value & ZERO_DETECT_TASK_NOTIFICATION_OVERFLOW) {
                zero_detect_process_overflow(handle, &g_overflow_data);
            }

            /* 处理错误事件 */
            if (notification_value & ZERO_DETECT_TASK_NOTIFICATION_ERROR) {
                zero_detect_process_error(handle, &g_error_data);
            }

            /* 处理停止事件 */
            if (notification_value & ZERO_DETECT_TASK_NOTIFICATION_STOP) {
                ZERO_DETECT_LOG_INFO("Received stop notification, exiting task");
                break;
            }
        }
    }

    ZERO_DETECT_LOG_INFO("ZeroDetectTask exited");
    vTaskDelete(NULL);
}

/******************************************************************************
 * @brief 处理零点捕获事件
 *
 * @param  handle 零点检测句柄
 * @param  data 零点捕获数据
 *
 * @return zero_detect_ret_t
 *****************************************************************************/
static zero_detect_ret_t zero_detect_process_zero_cross(zero_detect_handle_t *handle,
                                                       const zero_detect_zero_cross_data_t *data)
{
    if (handle == NULL || data == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    ZERO_DETECT_LOG_DEBUG("Zero cross: count=%u, t_zero=%u, freq=%.2f",
                          data->capture_count, data->t_zero, data->frequency);

    xSemaphoreTake(handle->mutex, portMAX_DELAY);

    /* 更新状态 */
    handle->status.capture_count = data->capture_count;
    handle->status.t_zero = data->t_zero;
    handle->status.total_zero_cross_count++;

    /* 验证频率有效性 */
    if (zero_detect_is_frequency_valid(data->frequency)) {
        handle->status.grid_freq_hz = data->frequency;
        handle->status.freq_valid = true;
        handle->freq_calc_cycle_count++;

        /* 按文档要求每5个周期更新一次频率 */
        if (handle->freq_calc_cycle_count >= handle->config.freq_update_cycles) {
            handle->status.freq_update_count++;
            handle->freq_calc_cycle_count = 0;
            ZERO_DETECT_LOG_INFO("Grid frequency updated: %.2f Hz", data->frequency);
        }
    } else {
        handle->status.freq_valid = false;
        ZERO_DETECT_LOG_ERROR("Invalid grid frequency: %.2f Hz", data->frequency);
    }

    /* 检查溢出 */
    if (data->capture_count >= handle->config.capture_overflow_threshold) {
        ZERO_DETECT_LOG_ERROR("Capture count approaching overflow: %u", data->capture_count);
        handle->status.overflow_count++;
    }

    xSemaphoreGive(handle->mutex);

    /* 通知观察者 */
    zero_detect_notify_observers_zero_cross(handle, data->capture_count, data->t_zero, data->frequency);

    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 处理信号频率事件
 *
 * @param  handle 零点检测句柄
 * @param  data 信号频率数据
 *
 * @return zero_detect_ret_t
 *****************************************************************************/
static zero_detect_ret_t zero_detect_process_signal_freq(zero_detect_handle_t *handle,
                                                        const zero_detect_signal_freq_data_t *data)
{
    if (handle == NULL || data == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    ZERO_DETECT_LOG_DEBUG("Signal frequency: %.2f Hz", data->frequency);

    xSemaphoreTake(handle->mutex, portMAX_DELAY);
    if (zero_detect_is_frequency_valid(data->frequency)) {
        handle->status.signal_freq_hz = data->frequency;
        ZERO_DETECT_LOG_DEBUG("Signal frequency updated: %.2f Hz", data->frequency);
    } else {
        ZERO_DETECT_LOG_ERROR("Invalid signal frequency: %.2f Hz", data->frequency);
    }
    xSemaphoreGive(handle->mutex);

    /* 通知观察者 */
    zero_detect_notify_observers_signal_freq(handle, data->frequency);

    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 处理溢出事件
 *
 * @param  handle 零点检测句柄
 * @param  data 溢出数据
 *
 * @return zero_detect_ret_t
 *****************************************************************************/
static zero_detect_ret_t zero_detect_process_overflow(zero_detect_handle_t *handle,
                                                     const zero_detect_overflow_data_t *data)
{
    if (handle == NULL || data == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    ZERO_DETECT_LOG_ERROR("Overflow detected, old_count: %u", data->old_capture_count);

    xSemaphoreTake(handle->mutex, portMAX_DELAY);
    handle->status.overflow_count++;
    /* 重置相关计数器 */
    handle->freq_calc_cycle_count = 0;
    handle->last_zero_cross_time = 0;
    xSemaphoreGive(handle->mutex);

    /* 通知观察者 */
    zero_detect_notify_observers_overflow(handle, data->old_capture_count);

    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 处理错误事件
 *
 * @param  handle 零点检测句柄
 * @param  data 错误数据
 *
 * @return zero_detect_ret_t
 *****************************************************************************/
static zero_detect_ret_t zero_detect_process_error(zero_detect_handle_t *handle,
                                                   const zero_detect_error_data_t *data)
{
    if (handle == NULL || data == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    ZERO_DETECT_LOG_ERROR("Error event: code=%d, msg=%s", data->error_code,
                          data->error_msg ? data->error_msg : "NULL");

    /* 通知观察者 */
    zero_detect_notify_observers_error(handle, (zero_detect_ret_t)data->error_code, data->error_msg);

    return ZERO_DETECT_OK;
}

/******************************************************************************
 * @brief 检查频率是否有效
 *
 * @param  frequency 频率值
 *
 * @return bool
 *****************************************************************************/
static bool zero_detect_is_frequency_valid(float frequency)
{
    return (frequency >= ZERO_DETECT_MIN_VALID_FREQ_HZ &&
            frequency <= ZERO_DETECT_MAX_VALID_FREQ_HZ);
}

/******************************************************************************
 * @brief 通知观察者零点捕获事件
 *
 * @param  handle 零点检测句柄
 * @param  capture_count 捕获计数
 * @param  t_zero 零点时间戳
 * @param  frequency 频率值
 *
 * @return void
 *****************************************************************************/
static void zero_detect_notify_observers_zero_cross(zero_detect_handle_t *handle,
                                                   uint16_t capture_count, uint16_t t_zero, float frequency)
{
    if (handle == NULL) {
        return;
    }

    xSemaphoreTake(handle->mutex, portMAX_DELAY);
    for (uint8_t i = 0; i < handle->callback_count; i++) {
        if (handle->callbacks[i].on_zero_cross != NULL) {
            handle->callbacks[i].on_zero_cross(capture_count, t_zero, frequency,
                                              handle->callbacks[i].user_data);
        }
    }
    xSemaphoreGive(handle->mutex);
}

/******************************************************************************
 * @brief 通知观察者信号频率事件
 *
 * @param  handle 零点检测句柄
 * @param  frequency 频率值
 *
 * @return void
 *****************************************************************************/
static void zero_detect_notify_observers_signal_freq(zero_detect_handle_t *handle, float frequency)
{
    if (handle == NULL) {
        return;
    }

    xSemaphoreTake(handle->mutex, portMAX_DELAY);
    for (uint8_t i = 0; i < handle->callback_count; i++) {
        if (handle->callbacks[i].on_signal_freq != NULL) {
            handle->callbacks[i].on_signal_freq(frequency, handle->callbacks[i].user_data);
        }
    }
    xSemaphoreGive(handle->mutex);
}

/******************************************************************************
 * @brief 通知观察者溢出事件
 *
 * @param  handle 零点检测句柄
 * @param  old_capture_count 旧的捕获计数
 *
 * @return void
 *****************************************************************************/
static void zero_detect_notify_observers_overflow(zero_detect_handle_t *handle, uint16_t old_capture_count)
{
    if (handle == NULL) {
        return;
    }

    xSemaphoreTake(handle->mutex, portMAX_DELAY);
    for (uint8_t i = 0; i < handle->callback_count; i++) {
        if (handle->callbacks[i].on_overflow != NULL) {
            handle->callbacks[i].on_overflow(old_capture_count, handle->callbacks[i].user_data);
        }
    }
    xSemaphoreGive(handle->mutex);
}

/******************************************************************************
 * @brief 通知观察者错误事件
 *
 * @param  handle 零点检测句柄
 * @param  error_code 错误代码
 * @param  error_msg 错误消息
 *
 * @return void
 *****************************************************************************/
static void zero_detect_notify_observers_error(zero_detect_handle_t *handle,
                                              zero_detect_ret_t error_code, const char *error_msg)
{
    if (handle == NULL) {
        return;
    }

    xSemaphoreTake(handle->mutex, portMAX_DELAY);
    for (uint8_t i = 0; i < handle->callback_count; i++) {
        if (handle->callbacks[i].on_error != NULL) {
            handle->callbacks[i].on_error(error_code, error_msg, handle->callbacks[i].user_data);
        }
    }
    xSemaphoreGive(handle->mutex);
}
    if (handle == NULL || !handle->initialized) {
        printf("[ZeroDetect] Task not initialized\r\n");
        return;
    }

    zero_detect_status_t status;
    if (zero_detect_task_get_status(handle, &status) != ZERO_DETECT_OK) {
        printf("[ZeroDetect] Failed to get status\r\n");
        return;
    }

    printf("=== Zero Detect Task Status ===\r\n");
    printf("Initialized: %s\r\n", handle->initialized ? "Yes" : "No");
    printf("Running: %s\r\n", handle->running ? "Yes" : "No");
    printf("Capture Count: %u\r\n", status.capture_count);
    printf("T Zero: %u\r\n", status.t_zero);
    printf("Grid Frequency: %.2f Hz (Valid: %s)\r\n",
           status.grid_freq_hz, status.freq_valid ? "Yes" : "No");
    printf("Signal Frequency: %.2f Hz\r\n", status.signal_freq_hz);
    printf("Total Zero Cross: %u\r\n", (unsigned int)status.total_zero_cross_count);
    printf("Freq Updates: %u\r\n", (unsigned int)status.freq_update_count);
    printf("Overflow Count: %u\r\n", (unsigned int)status.overflow_count);
    printf("===============================\r\n");
}

/**
 * @brief 执行自检
 */
zero_detect_ret_t zero_detect_task_self_test(zero_detect_handle_t *handle)
{
    if (handle == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    ZERO_DETECT_LOG_INFO("Starting self test...");

    /* 检查初始化状态 */
    if (!handle->initialized) {
        ZERO_DETECT_LOG_ERROR("Self test failed: not initialized");
        return ZERO_DETECT_ERROR_NOT_INIT;
    }

    /* 检查队列 */
    if (handle->event_queue == NULL) {
        ZERO_DETECT_LOG_ERROR("Self test failed: event queue is NULL");
        return ZERO_DETECT_ERROR;
    }

    /* 检查配置参数 */
    if (handle->config.task_stack_size == 0 ||
        handle->config.event_queue_size == 0 ||
        handle->config.freq_update_cycles == 0) {
        ZERO_DETECT_LOG_ERROR("Self test failed: invalid configuration");
        return ZERO_DETECT_ERROR;
    }

    ZERO_DETECT_LOG_INFO("Self test passed");
    return ZERO_DETECT_OK;
}
