#ifndef __TIMESTAMP_HANDLER_H__
#define __TIMESTAMP_HANDLER_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>
#include "timestamp_config.h"
#include "timestamp_utils.h"
#include "timestamp_driver.h"

/* 时间戳处理器 - 业务逻辑层 */

/* 处理器返回值 */
typedef enum {
    TIMESTAMP_HANDLER_OK = 0,
    TIMESTAMP_HANDLER_ERROR = -1,
    TIMESTAMP_HANDLER_ERROR_PARAM = -2,
    TIMESTAMP_HANDLER_ERROR_NOT_INIT = -3,
    TIMESTAMP_HANDLER_ERROR_OVERFLOW = -4
} timestamp_handler_ret_t;

/* 电网同步状态结构体 - 按文档规范 */
typedef struct {
    uint16_t capture_count;     /* 过零点捕获计数 */
    uint16_t t_zero;           /* 最近一次过零点时间戳 (16位CNT值) */
    float grid_freq_hz;        /* 电网频率估算值 */
    float signal_freq_hz;      /* 采样信号频率估算值 */
} timestamp_grid_sync_status_t;

/* 频率测量策略接口 */
typedef struct {
    const char *name;           /* 策略名称 */
    
    /* 初始化策略 */
    timestamp_handler_ret_t (*init)(void *strategy_data, const timestamp_config_t *config);
    
    /* 处理新的捕获数据 */
    timestamp_handler_ret_t (*process)(void *strategy_data, uint16_t period_ticks, 
                                     timestamp_freq_result_t *result);
    
    /* 重置策略状态 */
    timestamp_handler_ret_t (*reset)(void *strategy_data);
    
    /* 获取策略状态 */
    timestamp_handler_ret_t (*get_status)(void *strategy_data, void *status);
    
} timestamp_freq_strategy_interface_t;

/* 事件观察者接口 */
typedef struct {
    /* 零点捕获事件 */
    void (*on_zero_cross)(uint16_t capture_count, uint16_t t_zero, float frequency, void *user_data);
    
    /* 信号频率更新事件 */
    void (*on_signal_freq_update)(float frequency, void *user_data);
    
    /* 溢出事件 */
    void (*on_overflow)(uint16_t old_capture_count, void *user_data);
    
    /* 错误事件 */
    void (*on_error)(timestamp_handler_ret_t error_code, const char *error_msg, void *user_data);
    
} timestamp_event_observer_t;

/* 时间戳处理器句柄 */
typedef struct {
    /* 配置和状态 */
    timestamp_config_t config;                      /* 配置参数 */
    timestamp_grid_sync_status_t sync_status;       /* 电网同步状态 */
    timestamp_statistics_t statistics;              /* 统计信息 */
    bool initialized;                               /* 初始化标志 */
    bool overflow_reset_pending;                    /* 溢出重置待处理标志 */
    
    /* 驱动层接口 */
    timestamp_driver_handle_t driver;               /* 驱动句柄 */
    
    /* 频率测量策略 */
    const timestamp_freq_strategy_interface_t *grid_freq_strategy;    /* 电网频率策略 */
    const timestamp_freq_strategy_interface_t *signal_freq_strategy;  /* 信号频率策略 */
    void *grid_strategy_data;                       /* 电网频率策略数据 */
    void *signal_strategy_data;                     /* 信号频率策略数据 */
    
    /* 事件观察者 */
    timestamp_event_observer_t observer;            /* 事件观察者 */
    void *observer_user_data;                       /* 观察者用户数据 */
    
    /* 内部状态 */
    uint16_t last_grid_t_zero;                      /* 上一次电网过零时间 */
    uint16_t last_signal_capture;                   /* 上一次信号捕获时间 */
    timestamp_freq_result_t grid_freq_result;       /* 电网频率测量结果 */
    timestamp_freq_result_t signal_freq_result;     /* 信号频率测量结果 */
    
} timestamp_handler_handle_t;

/* 处理器接口函数 */

/**
 * @brief 初始化时间戳处理器
 * @param handle 处理器句柄
 * @param config 配置参数
 * @return timestamp_handler_ret_t 返回值
 */
timestamp_handler_ret_t timestamp_handler_init(timestamp_handler_handle_t *handle,
                                              const timestamp_config_t *config);

/**
 * @brief 反初始化时间戳处理器
 * @param handle 处理器句柄
 * @return timestamp_handler_ret_t 返回值
 */
timestamp_handler_ret_t timestamp_handler_deinit(timestamp_handler_handle_t *handle);

/**
 * @brief 启动时间戳处理器
 * @param handle 处理器句柄
 * @return timestamp_handler_ret_t 返回值
 */
timestamp_handler_ret_t timestamp_handler_start(timestamp_handler_handle_t *handle);

/**
 * @brief 停止时间戳处理器
 * @param handle 处理器句柄
 * @return timestamp_handler_ret_t 返回值
 */
timestamp_handler_ret_t timestamp_handler_stop(timestamp_handler_handle_t *handle);

/**
 * @brief 设置频率测量策略
 * @param handle 处理器句柄
 * @param tim_type 定时器类型
 * @param strategy 策略接口
 * @param strategy_data 策略数据
 * @return timestamp_handler_ret_t 返回值
 */
timestamp_handler_ret_t timestamp_handler_set_freq_strategy(timestamp_handler_handle_t *handle,
                                                           timestamp_driver_tim_type_t tim_type,
                                                           const timestamp_freq_strategy_interface_t *strategy,
                                                           void *strategy_data);

/**
 * @brief 注册事件观察者
 * @param handle 处理器句柄
 * @param observer 观察者接口
 * @param user_data 用户数据
 * @return timestamp_handler_ret_t 返回值
 */
timestamp_handler_ret_t timestamp_handler_register_observer(timestamp_handler_handle_t *handle,
                                                           const timestamp_event_observer_t *observer,
                                                           void *user_data);

/**
 * @brief 获取电网同步状态
 * @param handle 处理器句柄
 * @param status 输出状态
 * @return timestamp_handler_ret_t 返回值
 */
timestamp_handler_ret_t timestamp_handler_get_sync_status(timestamp_handler_handle_t *handle,
                                                         timestamp_grid_sync_status_t *status);

/**
 * @brief 生成压缩时间戳
 * @param handle 处理器句柄
 * @param t_sample0 采样起始时间
 * @return uint32_t 压缩时间戳
 */
uint32_t timestamp_handler_generate_packed_timestamp(timestamp_handler_handle_t *handle,
                                                    uint16_t t_sample0);

/**
 * @brief 计算相位修正参数
 * @param handle 处理器句柄
 * @param packed_timestamp 压缩时间戳
 * @param correction 输出相位修正参数
 * @return timestamp_handler_ret_t 返回值
 */
timestamp_handler_ret_t timestamp_handler_calculate_phase_correction(timestamp_handler_handle_t *handle,
                                                                    uint32_t packed_timestamp,
                                                                    timestamp_phase_correction_t *correction);

/**
 * @brief 检查并处理溢出
 * @param handle 处理器句柄
 * @return timestamp_handler_ret_t 返回值
 */
timestamp_handler_ret_t timestamp_handler_check_overflow(timestamp_handler_handle_t *handle);

/**
 * @brief 获取统计信息
 * @param handle 处理器句柄
 * @param statistics 输出统计信息
 * @return timestamp_handler_ret_t 返回值
 */
timestamp_handler_ret_t timestamp_handler_get_statistics(timestamp_handler_handle_t *handle,
                                                        timestamp_statistics_t *statistics);

/**
 * @brief 重置统计信息
 * @param handle 处理器句柄
 * @return timestamp_handler_ret_t 返回值
 */
timestamp_handler_ret_t timestamp_handler_reset_statistics(timestamp_handler_handle_t *handle);

/* 内置频率测量策略 */

/**
 * @brief 获取简单频率测量策略
 * @return const timestamp_freq_strategy_interface_t* 策略接口
 */
const timestamp_freq_strategy_interface_t* timestamp_handler_get_simple_freq_strategy(void);

/**
 * @brief 获取滤波频率测量策略
 * @return const timestamp_freq_strategy_interface_t* 策略接口
 */
const timestamp_freq_strategy_interface_t* timestamp_handler_get_filtered_freq_strategy(void);

/**
 * @brief 获取自适应频率测量策略
 * @return const timestamp_freq_strategy_interface_t* 策略接口
 */
const timestamp_freq_strategy_interface_t* timestamp_handler_get_adaptive_freq_strategy(void);

#ifdef __cplusplus
}
#endif

#endif /* __TIMESTAMP_HANDLER_H__ */
