{"name": "Busbar_Voltage_Online_Monitor", "target": "Busbar_Voltage_Online_Monitor", "toolchain": "AC5", "toolchainLocation": "C:\\Keil_v5\\ARM\\ARMCC", "toolchainCfgFile": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.13\\res\\data\\models/arm.v5.model.json", "buildMode": "fast|multhread", "showRepathOnLog": true, "threadNum": 16, "rootDir": "d:\\arrester_insulation_monitoring\\04_Software\\02_Busbar_Voltage_Online_Monitor\\EIDE_PRJ", "dumpPath": "build\\Busbar_Voltage_Online_Monitor", "outDir": "build\\Busbar_Voltage_Online_Monitor", "incDirs": [".", "../Core/Inc", "../Drivers/STM32F4xx_HAL_Driver/Inc", "../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy", "../Middlewares/Third_Party/FreeRTOS/Source/include", "../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2", "../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F", "../Drivers/CMSIS/Device/ST/STM32F4xx/Include", "../Drivers/CMSIS/Include", "../BSP/BSP_AD7606/hal_driver/Src", "../BSP/BSP_AD7606/hal_driver/Inc", "../MCU_Peripherals_Drivers/Inc", "../MCU_Peripherals_Drivers/Src", "../Middlewares/linked_list", "../easylogger/inc", "../easylogger/src", "../easylogger/port", "../RTT/Config", "../RTT/RTT", "../BSP/BSP_LED/hal_driver/Inc", "../BSP/BSP_LED/hal_driver/Src", "../unit_test/Inc", "../unit_test/Src", "../BSP/BSP_Analog_Switch/hal_driver/Inc", "../BSP/BSP_Analog_Switch/hal_driver/Src", "../BSP/BSP_AD7606/handler/Inc", "../BSP/BSP_AD7606/handler/Src", "../BSP/BSP_Analog_Switch/handler/Inc", "../BSP/BSP_Analog_Switch/handler/Src", "../Middlewares/cm_backtrace", "../Middlewares/cm_backtrace/Languages/en-US", "../Middlewares/cm_backtrace/Languages/zh-CN", "../Tasks/Data_Process_Task/Inc", "../Tasks/Data_Process_Task/Src", "../Tasks/FFT_Task/Inc", "../Tasks/FFT_Task/Src", "../SYSTEM/Inc", "../SYSTEM/Src", "../Middlewares/Algorithm", "../Drivers/CMSIS/DSP/Include", "../Drivers/CMSIS/DSP/PrivateInclude", "../Drivers/CMSIS/DSP/Source/TransformFunctions", "../BSP/BSP_RTC/hal_driver/Inc", "../BSP/BSP_RTC/hal_driver/Src", "../BSP/BSP_RTC/handler/Inc", "../BSP/BSP_RTC/handler/Src", "../Tasks/Thunderstrike_Task/Inc", "../Tasks/Thunderstrike_Task/Src", "../Middlewares/RingBuff/Inc", "../Middlewares/RingBuff/Src", "../Middlewares/Systemview/Config", "../Middlewares/Systemview/Sample/FreeRTOSV10.4", "../Middlewares/Systemview/SEGGER", "../Drivers/CMSIS/DSP/Source/FastMathFunctions", "../Middlewares/LPF_FIR_Algorithm/Src", "../Middlewares/LPF_FIR_Algorithm/Inc", "../Busbar_Voltage_Online_Monitor/Flash/handler/Inc", "../Busbar_Voltage_Online_Monitor/Flash/handler/Src", "../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Inc", "../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Src", "../Busbar_Voltage_Online_Monitor/RS485/handler/Inc", "../Busbar_Voltage_Online_Monitor/RS485/handler/Src", "../Busbar_Voltage_Online_Monitor/Uart_dev/Inc", "../Busbar_Voltage_Online_Monitor/Uart_dev/Src", ".cmsis/include", "../MDK-ARM/RTE/_Busbar_Voltage_Online_Monitor", "../Middlewares/OSAL/src", "../Middlewares/OSAL/inc", "../BSP/BSP_LED/handler/Inc", "../BSP/BSP_LED/handler/Src"], "libDirs": [], "defines": ["USE_HAL_DRIVER", "STM32F411xE", "USE_FULL_ASSERT", "ARM_MATH_CM4", "__CC_ARM", "ARM_MATH_MATRIX_CHECK", "ARM_MATH_ROUNDING"], "sourceList": ["../BSP/BSP_AD7606/hal_driver/Src/ad7606_driver.c", "../BSP/BSP_AD7606/handler/Src/bsp_adc_collect_xxx_handler.c", "../BSP/BSP_LED/hal_driver/Src/bsp_led_driver.c", "../BSP/BSP_LED/handler/Src/bsp_led_handler.c", "../BSP/BSP_LED/handler/Src/uart_led_indicator.c", "../Busbar_Voltage_Online_Monitor/Flash/handler/Src/flash_event_fun.c", "../Busbar_Voltage_Online_Monitor/Flash/handler/Src/flash_event_handler.c", "../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Src/modbus_rtu.c", "../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Src/reg_adress_msg.c", "../Busbar_Voltage_Online_Monitor/RS485/hal_driver/Src/rs485_dev.c", "../Busbar_Voltage_Online_Monitor/RS485/handler/Src/rs485_event_handler.c", "../Busbar_Voltage_Online_Monitor/Uart_dev/Src/uart_dev.c", "../Core/Src/crc.c", "../Core/Src/dma.c", "../Core/Src/freertos.c", "../Core/Src/gpio.c", "../Core/Src/main.c", "../Core/Src/rtc.c", "../Core/Src/spi.c", "../Core/Src/stm32f4xx_hal_msp.c", "../Core/Src/stm32f4xx_hal_timebase_tim.c", "../Core/Src/stm32f4xx_it.c", "../Core/Src/system_stm32f4xx.c", "../Core/Src/tim.c", "../Core/Src/usart.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_crc.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rtc_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c", "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c", "../MCU_Peripherals_Drivers/Src/mcu_flash_driver.c", "../MCU_Peripherals_Drivers/Src/mcu_gpio_driver.c", "../MCU_Peripherals_Drivers/Src/mcu_spi_driver.c", "../MCU_Peripherals_Drivers/Src/mcu_uart_driver.c", "../MDK-ARM/startup_stm32f411xe.s", "../Middlewares/Algorithm/algorithm.c", "../Middlewares/LPF_FIR_Algorithm/Src/lpf_fir_alogorithm.c", "../Middlewares/OSAL/src/os_adaptation.c", "../Middlewares/OSAL/src/os_critical.c", "../Middlewares/OSAL/src/os_event_group.c", "../Middlewares/OSAL/src/os_queue.c", "../Middlewares/OSAL/src/os_semaphore.c", "../Middlewares/OSAL/src/os_task.c", "../Middlewares/OSAL/src/os_timer.c", "../Middlewares/RingBuff/Src/ringbuff.c", "../Middlewares/ST/ARM/DSP/Lib/arm_cortexM4lf_math.lib", "../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS_V2/cmsis_os2.c", "../Middlewares/Third_Party/FreeRTOS/Source/croutine.c", "../Middlewares/Third_Party/FreeRTOS/Source/event_groups.c", "../Middlewares/Third_Party/FreeRTOS/Source/list.c", "../Middlewares/Third_Party/FreeRTOS/Source/portable/MemMang/heap_4.c", "../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM4F/port.c", "../Middlewares/Third_Party/FreeRTOS/Source/queue.c", "../Middlewares/Third_Party/FreeRTOS/Source/stream_buffer.c", "../Middlewares/Third_Party/FreeRTOS/Source/tasks.c", "../Middlewares/Third_Party/FreeRTOS/Source/timers.c", "../Middlewares/cm_backtrace/cm_backtrace.c", "../Middlewares/cm_backtrace/fault_handler/keil/cmb_fault.S", "../Middlewares/linked_list/linked_list.c", "../RTT/RTT/SEGGER_RTT.c", "../RTT/RTT/SEGGER_RTT_printf.c", "../SYSTEM/Src/system_adaption.c", "../Tasks/Data_Process_Task/Src/data_proc_calcu_fun.c", "../Tasks/Data_Process_Task/Src/data_process_task.c", "../Tasks/FFT_Task/Src/fft_calculate_task.c", "../easylogger/port/elog_port_improved.c", "../easylogger/src/elog.c", "../easylogger/src/elog_async.c", "../easylogger/src/elog_utils.c", "../unit_test/Src/ad7606_test.c", "../unit_test/Src/fft_result_test.c", "../unit_test/Src/led_test.c"], "alwaysInBuildSources": [], "sourceParams": {}, "options": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "[Copy linker output for Keil User Commands]", "command": "cd .\\..\\MDK-ARM && mkdir ${KEIL_OUTPUT_DIR} & copy \"${OutDir}\\${ProjectName}.axf\" \"${KEIL_OUTPUT_DIR}\\${ProjectName}.axf\"", "disable": false, "abortAfterFailed": true}, {"name": "keil-build-viewer.exe", "command": "cd .\\..\\MDK-ARM && keil-build-viewer.exe", "disable": false, "abortAfterFailed": true}, {"name": "axf to elf", "command": "axf2elf -d \"${ToolchainRoot}\" -i \"${OutDir}/${ProjectName}.axf\" -o \"${OutDir}/${ProjectName}.elf\" > \"${OutDir}/axf2elf.log\""}], "global": {"use-microLIB": true, "output-debug-info": "enable", "microcontroller-cpu": "cortex-m4-sp", "microcontroller-fpu": "cortex-m4-sp", "microcontroller-float": "cortex-m4-sp", "$arch-extensions": "", "$clang-arch-extensions": "", "$armlink-arch-extensions": ""}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=2803,1,1035", "CXX_FLAGS": "--diag_suppress=2803,1,1035", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"output-format": "elf", "link-scatter": ["d:/arrester_insulation_monitoring/04_Software/02_Busbar_Voltage_Online_Monitor/MDK-ARM/Busbar_Voltage_Online_Monitor/Busbar_Voltage_Online_Monitor.sct"]}}, "env": {"KEIL_OUTPUT_DIR": "Busbar_Voltage_Online_Monitor", "workspaceFolder": "d:\\arrester_insulation_monitoring\\04_Software\\02_Busbar_Voltage_Online_Monitor\\EIDE_PRJ", "workspaceFolderBasename": "EIDE_PRJ", "OutDir": "d:\\arrester_insulation_monitoring\\04_Software\\02_Busbar_Voltage_Online_Monitor\\EIDE_PRJ\\build\\Busbar_Voltage_Online_Monitor", "OutDirRoot": "build", "OutDirBase": "build\\Busbar_Voltage_Online_Monitor", "ProjectName": "Busbar_Voltage_Online_Monitor", "ConfigName": "Busbar_Voltage_Online_Monitor", "ProjectRoot": "d:\\arrester_insulation_monitoring\\04_Software\\02_Busbar_Voltage_Online_Monitor\\EIDE_PRJ", "ExecutableName": "d:\\arrester_insulation_monitoring\\04_Software\\02_Busbar_Voltage_Online_Monitor\\EIDE_PRJ\\build\\Busbar_Voltage_Online_Monitor\\Busbar_Voltage_Online_Monitor", "ChipPackDir": "", "ChipName": "", "SYS_Platform": "win32", "SYS_DirSep": "\\", "SYS_DirSeparator": "\\", "SYS_PathSep": ";", "SYS_PathSeparator": ";", "SYS_EOL": "\r\n", "EIDE_BUILDER_DIR": "c:\\Users\\<USER>\\.vscode\\extensions\\cl.eide-3.23.13\\res\\tools\\win32\\unify_builder", "EIDE_BINARIES_VER": "12.1.1", "EIDE_MSYS": "C:\\Users\\<USER>\\.eide\\bin\\builder\\msys\\bin", "EIDE_PY3_CMD": "C:\\Users\\<USER>\\.eide\\bin\\python36\\python3.exe", "ToolchainRoot": "C:\\Keil_v5\\ARM\\ARMCC"}, "sysPaths": []}