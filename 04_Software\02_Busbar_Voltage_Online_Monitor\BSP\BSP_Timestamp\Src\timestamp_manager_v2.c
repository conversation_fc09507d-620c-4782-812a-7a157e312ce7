#include "timestamp_manager_v2.h"
#include "tim.h"
#include <stdio.h>
#include <string.h>

/* 全局管理器句柄 */
static timestamp_handler_handle_t g_timestamp_handler = {0};
static timestamp_manager_config_t g_manager_config = {0};
static timestamp_manager_status_t g_manager_status = {0};
static timestamp_manager_callbacks_t g_callbacks = {0};

/* 外部定时器句柄 */
extern TIM_HandleTypeDef htim3;
extern TIM_HandleTypeDef htim4;

/* 内部函数声明 */
static void timestamp_manager_v2_event_observer(uint16_t capture_count, uint16_t t_zero, 
                                               float frequency, void *user_data);
static void timestamp_manager_v2_signal_freq_observer(float frequency, void *user_data);
static void timestamp_manager_v2_overflow_observer(uint16_t old_capture_count, void *user_data);
static void timestamp_manager_v2_error_observer(timestamp_handler_ret_t error_code, 
                                               const char *error_msg, void *user_data);

/**
 * @brief 初始化时间戳管理器
 */
timestamp_manager_ret_t timestamp_manager_v2_init(const timestamp_manager_config_t *config)
{
    /* 设置配置 */
    if (config != NULL) {
        g_manager_config = *config;
    } else {
        g_manager_config = timestamp_manager_v2_get_default_config();
    }

    /* 初始化处理器 */
    timestamp_handler_ret_t ret = timestamp_handler_init(&g_timestamp_handler, &g_manager_config.base_config);
    if (ret != TIMESTAMP_HANDLER_OK) {
        return TIMESTAMP_MANAGER_ERROR;
    }

    /* 注册事件观察者 */
    timestamp_event_observer_t observer = {
        .on_zero_cross = timestamp_manager_v2_event_observer,
        .on_signal_freq_update = timestamp_manager_v2_signal_freq_observer,
        .on_overflow = timestamp_manager_v2_overflow_observer,
        .on_error = timestamp_manager_v2_error_observer
    };
    timestamp_handler_register_observer(&g_timestamp_handler, &observer, NULL);

    /* 初始化管理器状态 */
    memset(&g_manager_status, 0, sizeof(g_manager_status));
    g_manager_status.initialized = true;
    g_manager_status.running = false;

    return TIMESTAMP_MANAGER_OK;
}

/**
 * @brief 反初始化时间戳管理器
 */
timestamp_manager_ret_t timestamp_manager_v2_deinit(void)
{
    if (!g_manager_status.initialized) {
        return TIMESTAMP_MANAGER_ERROR_NOT_INIT;
    }

    /* 停止管理器 */
    timestamp_manager_v2_stop();

    /* 反初始化处理器 */
    timestamp_handler_deinit(&g_timestamp_handler);

    /* 清零状态 */
    memset(&g_manager_status, 0, sizeof(g_manager_status));
    memset(&g_callbacks, 0, sizeof(g_callbacks));

    return TIMESTAMP_MANAGER_OK;
}

/**
 * @brief 启动时间戳管理器
 */
timestamp_manager_ret_t timestamp_manager_v2_start(void)
{
    if (!g_manager_status.initialized) {
        return TIMESTAMP_MANAGER_ERROR_NOT_INIT;
    }

    if (g_manager_status.running) {
        return TIMESTAMP_MANAGER_OK;  /* 已经在运行 */
    }

    /* 启动处理器 */
    timestamp_handler_ret_t ret = timestamp_handler_start(&g_timestamp_handler);
    if (ret != TIMESTAMP_HANDLER_OK) {
        return TIMESTAMP_MANAGER_ERROR;
    }

    g_manager_status.running = true;
    return TIMESTAMP_MANAGER_OK;
}

/**
 * @brief 停止时间戳管理器
 */
timestamp_manager_ret_t timestamp_manager_v2_stop(void)
{
    if (!g_manager_status.initialized) {
        return TIMESTAMP_MANAGER_ERROR_NOT_INIT;
    }

    if (!g_manager_status.running) {
        return TIMESTAMP_MANAGER_OK;  /* 已经停止 */
    }

    /* 停止处理器 */
    timestamp_handler_stop(&g_timestamp_handler);

    g_manager_status.running = false;
    return TIMESTAMP_MANAGER_OK;
}

/**
 * @brief 注册事件回调
 */
timestamp_manager_ret_t timestamp_manager_v2_register_callbacks(const timestamp_manager_callbacks_t *callbacks)
{
    if (!g_manager_status.initialized || callbacks == NULL) {
        return TIMESTAMP_MANAGER_ERROR_PARAM;
    }

    g_callbacks = *callbacks;
    return TIMESTAMP_MANAGER_OK;
}

/**
 * @brief 获取电网同步状态
 */
timestamp_manager_ret_t timestamp_manager_v2_get_sync_status(timestamp_grid_sync_status_t *status)
{
    if (!g_manager_status.initialized || status == NULL) {
        return TIMESTAMP_MANAGER_ERROR_PARAM;
    }

    timestamp_handler_ret_t ret = timestamp_handler_get_sync_status(&g_timestamp_handler, status);
    if (ret == TIMESTAMP_HANDLER_OK) {
        g_manager_status.sync_status = *status;
        return TIMESTAMP_MANAGER_OK;
    }

    return TIMESTAMP_MANAGER_ERROR;
}

/**
 * @brief 获取管理器状态
 */
timestamp_manager_ret_t timestamp_manager_v2_get_manager_status(timestamp_manager_status_t *status)
{
    if (status == NULL) {
        return TIMESTAMP_MANAGER_ERROR_PARAM;
    }

    /* 更新统计信息 */
    if (g_manager_status.initialized) {
        timestamp_handler_get_statistics(&g_timestamp_handler, &g_manager_status.statistics);
    }

    *status = g_manager_status;
    return TIMESTAMP_MANAGER_OK;
}

/**
 * @brief 生成压缩时间戳
 */
uint32_t timestamp_manager_v2_generate_packed_timestamp(void)
{
    if (!g_manager_status.initialized || !g_manager_status.running) {
        return 0;
    }

    /* 获取当前定时器计数值 */
    uint16_t current_count = 0;
    timestamp_driver_ret_t ret = timestamp_driver_get_counter(&g_timestamp_handler.driver,
                                                             TIMESTAMP_DRIVER_TIM_GRID_ZERO,
                                                             &current_count);
    if (ret != TIMESTAMP_DRIVER_OK) {
        return 0;
    }

    return timestamp_handler_generate_packed_timestamp(&g_timestamp_handler, current_count);
}

/**
 * @brief 计算相位修正参数
 */
timestamp_manager_ret_t timestamp_manager_v2_calculate_phase_correction(uint32_t packed_timestamp,
                                                                       timestamp_phase_correction_t *correction)
{
    if (!g_manager_status.initialized || correction == NULL) {
        return TIMESTAMP_MANAGER_ERROR_PARAM;
    }

    timestamp_handler_ret_t ret = timestamp_handler_calculate_phase_correction(&g_timestamp_handler,
                                                                              packed_timestamp,
                                                                              correction);
    return (ret == TIMESTAMP_HANDLER_OK) ? TIMESTAMP_MANAGER_OK : TIMESTAMP_MANAGER_ERROR;
}

/**
 * @brief 应用相位修正到FFT结果
 */
timestamp_manager_ret_t timestamp_manager_v2_apply_phase_correction(float *fft_complex_data,
                                                                   uint16_t fft_length,
                                                                   const timestamp_phase_correction_t *correction)
{
    if (fft_complex_data == NULL || correction == NULL) {
        return TIMESTAMP_MANAGER_ERROR_PARAM;
    }

    timestamp_utils_ret_t ret = timestamp_utils_apply_phase_correction(fft_complex_data, fft_length, correction);
    return (ret == TIMESTAMP_UTILS_OK) ? TIMESTAMP_MANAGER_OK : TIMESTAMP_MANAGER_ERROR;
}

/**
 * @brief 检查并处理溢出
 */
timestamp_manager_ret_t timestamp_manager_v2_check_overflow(void)
{
    if (!g_manager_status.initialized) {
        return TIMESTAMP_MANAGER_ERROR_NOT_INIT;
    }

    timestamp_handler_ret_t ret = timestamp_handler_check_overflow(&g_timestamp_handler);
    if (ret == TIMESTAMP_HANDLER_ERROR_OVERFLOW) {
        g_manager_status.overflow_count++;
        return TIMESTAMP_MANAGER_ERROR_OVERFLOW;
    }

    return (ret == TIMESTAMP_HANDLER_OK) ? TIMESTAMP_MANAGER_OK : TIMESTAMP_MANAGER_ERROR;
}

/* 事件观察者实现 */
static void timestamp_manager_v2_event_observer(uint16_t capture_count, uint16_t t_zero, 
                                               float frequency, void *user_data)
{
    if (g_callbacks.on_zero_cross) {
        g_callbacks.on_zero_cross(capture_count, t_zero, frequency, g_callbacks.user_data);
    }
}

static void timestamp_manager_v2_signal_freq_observer(float frequency, void *user_data)
{
    if (g_callbacks.on_signal_freq_update) {
        g_callbacks.on_signal_freq_update(frequency, g_callbacks.user_data);
    }
}

static void timestamp_manager_v2_overflow_observer(uint16_t old_capture_count, void *user_data)
{
    if (g_callbacks.on_overflow) {
        g_callbacks.on_overflow(old_capture_count, g_callbacks.user_data);
    }
}

static void timestamp_manager_v2_error_observer(timestamp_handler_ret_t error_code, 
                                               const char *error_msg, void *user_data)
{
    g_manager_status.error_count++;
    
    if (g_callbacks.on_error) {
        timestamp_manager_ret_t mgr_error = (error_code == TIMESTAMP_HANDLER_ERROR_OVERFLOW) ? 
                                           TIMESTAMP_MANAGER_ERROR_OVERFLOW : TIMESTAMP_MANAGER_ERROR;
        g_callbacks.on_error(mgr_error, error_msg, g_callbacks.user_data);
    }
}

/* 兼容性接口实现 */
void timestamp_manager_v2_tim3_ic_callback(void *htim)
{
    TIM_HandleTypeDef *tim_handle = (TIM_HandleTypeDef *)htim;
    if (tim_handle->Instance == TIM3 && tim_handle->Channel == HAL_TIM_ACTIVE_CHANNEL_1) {
        uint16_t capture_value = HAL_TIM_ReadCapturedValue(tim_handle, TIM_CHANNEL_1);
        timestamp_driver_global_tim3_callback(capture_value, TIMESTAMP_DRIVER_EVENT_RISING);
    }
}

void timestamp_manager_v2_tim4_ic_callback(void *htim)
{
    TIM_HandleTypeDef *tim_handle = (TIM_HandleTypeDef *)htim;
    if (tim_handle->Instance == TIM4 && tim_handle->Channel == HAL_TIM_ACTIVE_CHANNEL_1) {
        uint16_t capture_value = HAL_TIM_ReadCapturedValue(tim_handle, TIM_CHANNEL_1);
        timestamp_driver_global_tim4_callback(capture_value, TIMESTAMP_DRIVER_EVENT_RISING);
    }
}

/* 工厂函数实现 */
timestamp_manager_config_t timestamp_manager_v2_get_default_config(void)
{
    timestamp_manager_config_t config = {
        .base_config = timestamp_get_default_config(),
        .enable_debug_output = true,
        .enable_statistics = true,
        .auto_overflow_recovery = true
    };
    return config;
}

timestamp_manager_config_t timestamp_manager_v2_get_high_precision_config(void)
{
    timestamp_manager_config_t config = timestamp_manager_v2_get_default_config();
    config.base_config.freq_filter_alpha = 0.05f;  /* 更小的滤波系数，更高精度 */
    config.base_config.freq_update_period = 10;    /* 更长的更新周期 */
    return config;
}

timestamp_manager_config_t timestamp_manager_v2_get_low_power_config(void)
{
    timestamp_manager_config_t config = timestamp_manager_v2_get_default_config();
    config.base_config.freq_update_period = 3;     /* 更短的更新周期，降低功耗 */
    config.enable_debug_output = false;             /* 关闭调试输出 */
    config.enable_statistics = false;              /* 关闭统计功能 */
    return config;
}

/* 调试和诊断函数实现 */
void timestamp_manager_v2_print_status(void)
{
    printf("=== Timestamp Manager V2 Status ===\n");
    printf("Initialized: %s\n", g_manager_status.initialized ? "Yes" : "No");
    printf("Running: %s\n", g_manager_status.running ? "Yes" : "No");
    printf("Error count: %lu\n", g_manager_status.error_count);
    printf("Overflow count: %lu\n", g_manager_status.overflow_count);

    if (g_manager_status.initialized) {
        printf("Sync Status:\n");
        printf("  Capture count: %u\n", g_manager_status.sync_status.capture_count);
        printf("  T_zero: %u\n", g_manager_status.sync_status.t_zero);
        printf("  Grid frequency: %.3f Hz\n", g_manager_status.sync_status.grid_freq_hz);
        printf("  Signal frequency: %.3f Hz\n", g_manager_status.sync_status.signal_freq_hz);
    }
    printf("===================================\n");
}

void timestamp_manager_v2_print_statistics(void)
{
    if (!g_manager_status.initialized) {
        printf("Manager not initialized\n");
        return;
    }

    timestamp_statistics_t stats;
    if (timestamp_handler_get_statistics(&g_timestamp_handler, &stats) == TIMESTAMP_HANDLER_OK) {
        timestamp_utils_print_statistics(&stats);
    }
}

void timestamp_manager_v2_print_config(void)
{
    printf("=== Timestamp Manager V2 Config ===\n");
    timestamp_config_print(&g_manager_config.base_config);
    printf("Debug output: %s\n", g_manager_config.enable_debug_output ? "Enabled" : "Disabled");
    printf("Statistics: %s\n", g_manager_config.enable_statistics ? "Enabled" : "Disabled");
    printf("Auto overflow recovery: %s\n", g_manager_config.auto_overflow_recovery ? "Enabled" : "Disabled");
    printf("===================================\n");
}

timestamp_manager_ret_t timestamp_manager_v2_self_test(void)
{
    printf("=== Timestamp Manager V2 Self Test ===\n");

    /* 检查初始化状态 */
    if (!g_manager_status.initialized) {
        printf("FAIL: Manager not initialized\n");
        return TIMESTAMP_MANAGER_ERROR_NOT_INIT;
    }
    printf("PASS: Manager initialized\n");

    /* 检查驱动状态 */
    timestamp_driver_status_t driver_status;
    if (timestamp_driver_get_status(&g_timestamp_handler.driver, &driver_status) != TIMESTAMP_DRIVER_OK) {
        printf("FAIL: Cannot get driver status\n");
        return TIMESTAMP_MANAGER_ERROR;
    }
    printf("PASS: Driver status OK\n");

    /* 检查定时器计数 */
    uint16_t tim3_count, tim4_count;
    if (timestamp_driver_get_counter(&g_timestamp_handler.driver, TIMESTAMP_DRIVER_TIM_SIGNAL_FREQ, &tim3_count) != TIMESTAMP_DRIVER_OK) {
        printf("FAIL: Cannot read TIM3 counter\n");
        return TIMESTAMP_MANAGER_ERROR;
    }
    if (timestamp_driver_get_counter(&g_timestamp_handler.driver, TIMESTAMP_DRIVER_TIM_GRID_ZERO, &tim4_count) != TIMESTAMP_DRIVER_OK) {
        printf("FAIL: Cannot read TIM4 counter\n");
        return TIMESTAMP_MANAGER_ERROR;
    }
    printf("PASS: Timer counters OK (TIM3=%u, TIM4=%u)\n", tim3_count, tim4_count);

    /* 检查压缩时间戳生成 */
    uint32_t packed_ts = timestamp_manager_v2_generate_packed_timestamp();
    if (packed_ts == 0) {
        printf("FAIL: Cannot generate packed timestamp\n");
        return TIMESTAMP_MANAGER_ERROR;
    }
    printf("PASS: Packed timestamp generation OK (0x%08X)\n", packed_ts);

    printf("=== Self Test PASSED ===\n");
    return TIMESTAMP_MANAGER_OK;
}
