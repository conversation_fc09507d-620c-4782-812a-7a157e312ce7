/******************************************************************************
 * @file uart_led_indicator_v2_example.c
 * @brief UART LED指示器改进版使用示例
 * <AUTHOR>
 * @version 2.0
 * @date 2024-11-01
 *
 * @copyright Copyright (c) 2024
 *
 * 本示例展示了改进后的UART LED指示器设计：
 * 1. 明确的工作模式选择（轮询 vs 事件驱动）
 * 2. 语义化的通信状态回调（业务层面的状态通知）
 * 3. 分离的关注点（性能优化 vs 状态通知）
 * 4. 更直观的配置接口
 *
 * @note 1 tab == 4 spaces!
 *
 *****************************************************************************/

//******************************** Includes *********************************//
#include "uart_led_indicator.h"
#include "bsp_led_handler.h"
#include "system_adaption.h"
//******************************** Includes *********************************//

//******************************** Variables ********************************//
/* LED处理器实例 */
extern bsp_led_handler_t g_led_handler;
extern uart_led_indicator_timebase_interface_t led_handler_timebase_interface;

/* 统计数据 */
typedef struct {
    uint32_t comm_started_count;
    uint32_t comm_timeout_count;
    uint32_t total_data_packets;
    uint32_t active_sessions;
} comm_statistics_t;

static comm_statistics_t g_comm_stats = {0};
//******************************** Variables ********************************//

//******************************** Functions ********************************//

/**
 * @brief 通信状态回调函数示例
 * 
 * @param status 通信状态
 * @param p_user_data 用户数据
 */
void communication_status_callback(uart_comm_status_t status, void *p_user_data)
{
    comm_statistics_t *p_stats = (comm_statistics_t*)p_user_data;
    
    switch (status) {
        case UART_COMM_STATUS_STARTED:
            p_stats->comm_started_count++;
            p_stats->active_sessions++;
            printf("[COMM] Communication started (session #%lu, active: %lu)\n", 
                   p_stats->comm_started_count, p_stats->active_sessions);
            break;
            
        case UART_COMM_STATUS_ACTIVE:
            p_stats->total_data_packets++;
            printf("[COMM] Communication active (packet #%lu)\n", p_stats->total_data_packets);
            break;
            
        case UART_COMM_STATUS_TIMEOUT:
            if (p_stats->active_sessions > 0) {
                p_stats->active_sessions--;
            }
            p_stats->comm_timeout_count++;
            printf("[COMM] Communication timeout (total timeouts: %lu, active: %lu)\n", 
                   p_stats->comm_timeout_count, p_stats->active_sessions);
            break;
            
        case UART_COMM_STATUS_STOPPED:
            if (p_stats->active_sessions > 0) {
                p_stats->active_sessions--;
            }
            printf("[COMM] Communication stopped (active: %lu)\n", p_stats->active_sessions);
            break;
            
        default:
            printf("[COMM] Unknown status: %d\n", status);
            break;
    }
}

/**
 * @brief 事件驱动模式示例
 */
int event_driven_mode_example(void)
{
    printf("=== Event-Driven Mode Example ===\n");
    
    /* 配置事件驱动模式 */
    uart_led_indicator_config_t config = {
        .p_led_handler = &g_led_handler,
        .led_which = LED_485,
        .timeout_ms = 200,  /* 200ms超时 */
        .p_timebase_interface = &led_handler_timebase_interface,
        
        /* 明确选择事件驱动模式 */
        .work_mode = UART_LED_WORK_MODE_EVENT_DRIVEN,
        
        /* 语义化的状态回调 */
        .status_callback = communication_status_callback,
        .p_user_data = &g_comm_stats
    };
    
    /* 初始化 */
    uart_led_indicator_ret_code_t ret = uart_led_indicator_init(&config);
    if (ret != UART_LED_INDICATOR_OK) {
        printf("UART LED indicator init failed: %d\n", ret);
        return -1;
    }
    
    printf("UART LED indicator initialized in EVENT-DRIVEN mode\n");
    
    /* 模拟数据接收 */
    printf("\nSimulating data reception...\n");
    for (int i = 0; i < 5; i++) {
        printf("Receiving data packet %d\n", i + 1);
        uart_led_indicator_on_data_received();
        HAL_Delay(50);  /* 模拟数据包间隔 */
    }
    
    /* 等待超时 */
    printf("\nWaiting for timeout...\n");
    HAL_Delay(300);
    
    printf("Event-driven mode example completed\n\n");
    return 0;
}

/**
 * @brief 轮询模式示例
 */
int polling_mode_example(void)
{
    printf("=== Polling Mode Example ===\n");
    
    /* 重置统计数据 */
    memset(&g_comm_stats, 0, sizeof(g_comm_stats));
    
    /* 配置轮询模式 */
    uart_led_indicator_config_t config = {
        .p_led_handler = &g_led_handler,
        .led_which = LED_485,
        .timeout_ms = 150,  /* 150ms超时 */
        .p_timebase_interface = &led_handler_timebase_interface,
        
        /* 明确选择轮询模式 */
        .work_mode = UART_LED_WORK_MODE_POLLING,
        
        /* 仍然可以使用状态回调 */
        .status_callback = communication_status_callback,
        .p_user_data = &g_comm_stats
    };
    
    /* 初始化 */
    uart_led_indicator_ret_code_t ret = uart_led_indicator_init(&config);
    if (ret != UART_LED_INDICATOR_OK) {
        printf("UART LED indicator init failed: %d\n", ret);
        return -1;
    }
    
    printf("UART LED indicator initialized in POLLING mode\n");
    
    /* 模拟数据接收和轮询 */
    printf("\nSimulating data reception with polling...\n");
    for (int i = 0; i < 3; i++) {
        printf("Receiving data packet %d\n", i + 1);
        uart_led_indicator_on_data_received();
        
        /* 在轮询模式下需要周期调用task函数 */
        for (int j = 0; j < 20; j++) {  /* 模拟200ms的轮询 */
            uart_led_indicator_task();
            HAL_Delay(10);
        }
    }
    
    printf("Polling mode example completed\n\n");
    return 0;
}

/**
 * @brief 无回调模式示例
 */
int no_callback_mode_example(void)
{
    printf("=== No Callback Mode Example ===\n");
    
    /* 配置无回调模式 */
    uart_led_indicator_config_t config = {
        .p_led_handler = &g_led_handler,
        .led_which = LED_485,
        .timeout_ms = 100,  /* 100ms超时 */
        .p_timebase_interface = &led_handler_timebase_interface,
        
        /* 选择轮询模式 */
        .work_mode = UART_LED_WORK_MODE_POLLING,
        
        /* 不提供回调函数 */
        .status_callback = NULL,
        .p_user_data = NULL
    };
    
    /* 初始化 */
    uart_led_indicator_ret_code_t ret = uart_led_indicator_init(&config);
    if (ret != UART_LED_INDICATOR_OK) {
        printf("UART LED indicator init failed: %d\n", ret);
        return -1;
    }
    
    printf("UART LED indicator initialized without callback\n");
    
    /* 模拟数据接收 */
    printf("\nSimulating data reception (no callback)...\n");
    uart_led_indicator_on_data_received();
    
    /* 手动检查状态 */
    for (int i = 0; i < 15; i++) {
        uart_led_indicator_state_t state = uart_led_indicator_get_current_state();
        printf("Current state: %s\n", 
               (state == UART_LED_INDICATOR_STATE_IDLE) ? "IDLE" : "RECEIVING");
        
        uart_led_indicator_task();
        HAL_Delay(10);
    }
    
    printf("No callback mode example completed\n\n");
    return 0;
}

/**
 * @brief 设计对比演示
 */
void design_comparison_demo(void)
{
    printf("=== Design Comparison ===\n");
    
    printf("\n1. 旧设计的问题:\n");
    printf("   ❌ 隐式模式选择：回调函数存在与否决定性能模式\n");
    printf("   ❌ 职责混乱：回调函数既用于性能优化又用于状态通知\n");
    printf("   ❌ 技术状态暴露：用户需要理解IDLE/RECEIVING内部状态\n");
    printf("   ❌ 不直观：用户不知道提供回调会影响工作模式\n");
    
    printf("\n2. 新设计的优势:\n");
    printf("   ✅ 明确模式选择：work_mode字段直接指定工作模式\n");
    printf("   ✅ 关注点分离：性能优化和状态通知独立配置\n");
    printf("   ✅ 语义化回调：业务层面的通信状态（STARTED/ACTIVE/TIMEOUT/STOPPED）\n");
    printf("   ✅ 直观配置：每个配置项的作用都很清晰\n");
    
    printf("\n3. 使用场景:\n");
    printf("   📈 高性能场景：选择EVENT_DRIVEN模式，零轮询开销\n");
    printf("   🔄 兼容场景：选择POLLING模式，与现有代码兼容\n");
    printf("   📊 状态监控：提供status_callback获取业务状态通知\n");
    printf("   🎯 简单使用：不提供回调函数，仅使用LED指示功能\n");
}

/**
 * @brief 主函数示例
 */
int main(void)
{
    printf("UART LED Indicator V2.0 Examples\n");
    printf("================================\n\n");
    
    /* 1. 事件驱动模式示例 */
    if (event_driven_mode_example() != 0) {
        return -1;
    }
    
    /* 2. 轮询模式示例 */
    if (polling_mode_example() != 0) {
        return -1;
    }
    
    /* 3. 无回调模式示例 */
    if (no_callback_mode_example() != 0) {
        return -1;
    }
    
    /* 4. 设计对比演示 */
    design_comparison_demo();
    
    /* 5. 最终统计 */
    printf("\n=== Final Statistics ===\n");
    printf("Communication started: %lu times\n", g_comm_stats.comm_started_count);
    printf("Communication timeouts: %lu times\n", g_comm_stats.comm_timeout_count);
    printf("Total data packets: %lu\n", g_comm_stats.total_data_packets);
    printf("Active sessions: %lu\n", g_comm_stats.active_sessions);
    
    printf("\nAll examples completed successfully!\n");
    return 0;
}

//******************************** Functions ********************************//
