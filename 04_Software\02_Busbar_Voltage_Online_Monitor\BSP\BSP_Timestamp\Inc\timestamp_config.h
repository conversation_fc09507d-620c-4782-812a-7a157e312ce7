#ifndef __TIMESTAMP_CONFIG_H__
#define __TIMESTAMP_CONFIG_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>
#include <stdbool.h>

/* 时间戳模块配置参数 - 集中管理 */

/* 硬件配置 */
#define TIMESTAMP_TIMER_FREQ_HZ         1000000U    /* 定时器频率 1MHz */
#define TIMESTAMP_TIMER_BITS            16U         /* 16位定时器 */
#define TIMESTAMP_TIMER_MAX_VALUE       65535U      /* 16位定时器最大值 */

/* 采样配置 */
#define TIMESTAMP_SAMPLE_RATE_HZ        6400U       /* 采样率 6.4kHz */
#define TIMESTAMP_FFT_LENGTH            1024U       /* FFT长度 */

/* 频率测量配置 */
#define TIMESTAMP_NOMINAL_FREQ_HZ       50.0f       /* 标称频率 50Hz */
#define TIMESTAMP_MIN_VALID_FREQ_HZ     45.0f       /* 最小有效频率 */
#define TIMESTAMP_MAX_VALID_FREQ_HZ     65.0f       /* 最大有效频率 */
#define TIMESTAMP_FREQ_FILTER_ALPHA     0.1f        /* 频率滤波系数 */
#define TIMESTAMP_FREQ_UPDATE_PERIOD    5U          /* 频率更新周期（每5个周期） */

/* 溢出控制配置 - 按文档规范 */
#define TIMESTAMP_CAPTURE_OVERFLOW_THRESHOLD    0xFFFC  /* 溢出阈值 */

/* 调试配置 */
#define TIMESTAMP_DEBUG_ENABLE          1           /* 启用调试功能 */
#define TIMESTAMP_LOG_LEVEL             2           /* 日志级别：0=关闭，1=错误，2=信息，3=调试 */

/* 定时器通道配置 */
typedef enum {
    TIMESTAMP_TIM_CHANNEL_GRID_ZERO = 0,    /* TIM4 - 电网零点捕获 */
    TIMESTAMP_TIM_CHANNEL_SIGNAL_FREQ = 1,  /* TIM3 - 信号频率检测 */
    TIMESTAMP_TIM_CHANNEL_MAX
} timestamp_tim_channel_t;

/* 频率测量策略配置 */
typedef enum {
    TIMESTAMP_FREQ_STRATEGY_SIMPLE = 0,     /* 简单周期测量 */
    TIMESTAMP_FREQ_STRATEGY_FILTERED = 1,   /* 滤波测量 */
    TIMESTAMP_FREQ_STRATEGY_ADAPTIVE = 2,   /* 自适应测量 */
    TIMESTAMP_FREQ_STRATEGY_MAX
} timestamp_freq_strategy_t;

/* 时间戳模块配置结构体 */
typedef struct {
    uint32_t timer_freq_hz;                 /* 定时器频率 */
    uint32_t sample_rate_hz;                /* 采样率 */
    uint16_t fft_length;                    /* FFT长度 */
    float nominal_freq_hz;                  /* 标称频率 */
    float min_valid_freq_hz;                /* 最小有效频率 */
    float max_valid_freq_hz;                /* 最大有效频率 */
    float freq_filter_alpha;               /* 频率滤波系数 */
    uint16_t freq_update_period;            /* 频率更新周期 */
    uint16_t capture_overflow_threshold;    /* 捕获溢出阈值 */
    timestamp_freq_strategy_t freq_strategy; /* 频率测量策略 */
    bool debug_enable;                      /* 调试使能 */
} timestamp_config_t;

/* 默认配置获取函数 */
static inline timestamp_config_t timestamp_get_default_config(void)
{
    timestamp_config_t config = {
        .timer_freq_hz = TIMESTAMP_TIMER_FREQ_HZ,
        .sample_rate_hz = TIMESTAMP_SAMPLE_RATE_HZ,
        .fft_length = TIMESTAMP_FFT_LENGTH,
        .nominal_freq_hz = TIMESTAMP_NOMINAL_FREQ_HZ,
        .min_valid_freq_hz = TIMESTAMP_MIN_VALID_FREQ_HZ,
        .max_valid_freq_hz = TIMESTAMP_MAX_VALID_FREQ_HZ,
        .freq_filter_alpha = TIMESTAMP_FREQ_FILTER_ALPHA,
        .freq_update_period = TIMESTAMP_FREQ_UPDATE_PERIOD,
        .capture_overflow_threshold = TIMESTAMP_CAPTURE_OVERFLOW_THRESHOLD,
        .freq_strategy = TIMESTAMP_FREQ_STRATEGY_FILTERED,
        .debug_enable = TIMESTAMP_DEBUG_ENABLE
    };
    return config;
}

/* 配置验证函数 */
bool timestamp_config_validate(const timestamp_config_t *config);

/* 配置打印函数（调试用） */
void timestamp_config_print(const timestamp_config_t *config);

#ifdef __cplusplus
}
#endif

#endif /* __TIMESTAMP_CONFIG_H__ */
