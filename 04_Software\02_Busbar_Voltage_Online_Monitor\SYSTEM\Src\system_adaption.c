/******************************************************************************
 * @file system_adaption.c
 * @brief 
 * <AUTHOR>
 * @version 1.0
 * @date 2024-11-15
 * 
 * @copyright Copyright (c) 2024 
 * 
 * Processing flow:
 * 
 * @par dependencies
 * 
 * @note 1 tab == 4 spaces!
 * 
 *****************************************************************************/
#include "system_adaption.h"
#include "zero_detect_integration.h"
//******************************** Includes *********************************//


//******************************** Defines **********************************//

/*          From data process文件: api接口需求           */
#define DATA_MONITOR_LIMIT_NUMS 7
#define DATA_LIMIT_LARGER  16500.0f
#if CONFIG_USE_CURRENT_SENSOR
#define DATA_LIMIT_SMALLER 4850.0f
#else
#define DATA_LIMIT_SMALLER 5000.0f
#endif /* CONFIG_USE_CURRENT_SENSOR */
typedef struct
{
    adc_collect_set_range_t adc_range;          /*  adc采集范围 */
#if CONFIG_USE_CTR_AMP
    ctrl_amp_gain_t         ctrl_amp_gain;      /*  控制电路增益 */
#endif /* CONFIG_USE_CTR_AMP */
    float                   data_monitor_limit; /*  数据监测限值,单位mv */
}data_monitor_type_t;

/*          From data process文件: api接口需求           */

/*          From fft calculate文件: api接口需求          */
typedef struct
{
    fft_input_data_type_t   *input_data;                        /*  输入数据 */
    fft_data_type_t         data_type;                          /*  数据类型 */
    float                   adc_data[FFT_INPUT_QUANTITY];       /*  采样数据 */
}fft_calcu_adc_arg_t;
/*          From fft calculate文件: api接口需求          */

/*          回调函数类型定义                             */
//******************************** Variable *********************************//


/*          各线程栈空间分配                               */
sys_adaption_task_stack_t const g_sys_adaption_task_stack = 
{
    .adc_collect_event_thread_stack_size                 = 384,
    .adc_collect_set_event_thread_stack_size             = 128,
    .async_output_thread_stack_size                      = 128,
    .data_proc_handler_thread_stack_size                 = 384,
    .fft_event_handler_thread_stack_size                 = 512,
    .fft_collection_data_thread_stack_size               = 512,
    .flash_event_handler_thread_stack_size               = 128,
    .rs485_event_handle_thread_stack_size                = 128,
    .rs485_output_msg_thread_stack_size                  = 256,
    .system_visual_output_msg_stack_size                 = 128,
    .led_handler_thread_stack_size                       = 256,
    .zero_detect_task_stack_size                         = 512,    /* 零点检测任务栈大小 */
};

sys_adaption_task_priority_t const g_sys_adaption_task_priority = 
{
    .adc_collect_event_thread_priority                 = osPriorityHigh,
    .adc_collect_set_event_thread_priority             = osPriorityNormal6,
    .async_output_thread_priority                      = osPriorityBelowNormal,
    .data_proc_handler_thread_priority                 = osPriorityNormal5,
    .fft_event_handler_thread_priority                 = osPriorityAboveNormal7,
    .fft_collection_data_thread_priority               = osPriorityAboveNormal,
    .flash_event_handler_thread_priority               = osPriorityRealtime,
    .rs485_event_handle_thread_priority                = osPriorityHigh,
    .rs485_output_msg_thread_priority                  = osPriorityAboveNormal7,
    .system_visual_output_msg_priority                 = osPriorityNormal,
    .led_handler_thread_priority                       = osPriorityNormal,
    .zero_detect_task_priority                         = osPriorityAboveNormal,  /* 零点检测任务优先级 */
};
sys_adpation_queue_size_t const g_sys_adpation_queue_size = 
{
    .adc_collect_event_queue_size                 = 512,
    .adc_collect_set_event_queue_size             = 5,
    .data_proc_handler_queue_size                 = 32,
    .fft_finish_collection_queue_size             = 10,
    .flash_event_queue_size                       = 5,
    .rs485_event_queue_size                       = 32,
    .rtc_handler_event_queue_size                 = 10,
    .led_handler_event_queue_size                 = 32,
};
/*          From data process文件: api接口需求           */
static adc_collect_set_range_t g_adc_range_default = ADC_COLLECT_RANGE_5V;
#if CONFIG_USE_CTR_AMP
static data_monitor_type_t g_data_monitor_config_arry[DATA_MONITOR_LIMIT_NUMS] = 
{
    {
        .adc_range = ADC_COLLECT_RANGE_5V,
        .ctrl_amp_gain = CTRL_AMP_GAIN_900X,
        .data_monitor_limit = 3.50f,
    },
    {
        .adc_range = ADC_COLLECT_RANGE_5V,
        .ctrl_amp_gain = CTRL_AMP_GAIN_500X,
        .data_monitor_limit = 6.24f,
    },
    {
        .adc_range = ADC_COLLECT_RANGE_5V,
        .ctrl_amp_gain = CTRL_AMP_GAIN_100X,
        .data_monitor_limit = 29.14f,
    },
    {
        .adc_range = ADC_COLLECT_RANGE_5V,
        .ctrl_amp_gain = CTRL_AMP_GAIN_010X,
        .data_monitor_limit = 289.04f,
    },
    {
        .adc_range = ADC_COLLECT_RANGE_5V,
        .ctrl_amp_gain = CTRL_AMP_GAIN_005X,
        .data_monitor_limit = 545.29f,
    },
    {
        .adc_range = ADC_COLLECT_RANGE_5V,
        .ctrl_amp_gain = CTRL_AMP_GAIN_001X,
        .data_monitor_limit = 2884.61f,
    },
    {
        .adc_range = ADC_COLLECT_RANGE_10V,
        .ctrl_amp_gain = CTRL_AMP_GAIN_001X,
        .data_monitor_limit = DATA_LIMIT_LARGER,
    }
};
#endif /* CONFIG_USE_CTR_AMP */
/*          From data process文件: api接口需求           */

/*          From fft calculate文件: api接口需求          */
#ifdef FFT_CALCU_USING_IRQ
pfirq_callbackfun_t gp_fft_irq_callbackfun      = NULL;
void                *gp_fft_irq_callbackfun_arg = NULL;
#endif /* FFT_CALCU_USING_IRQ */
/*          From fft calculate文件: api接口需求          */

/*          From adc collect文件: api接口需求            */
pf_spi_irq_callbackfun_t g_spi_txcallback                 = NULL;
pf_spi_irq_callbackfun_t g_spi_rxcallback                 = NULL;
pf_irq_callback_fun_t    g_adc_busy_irq_callbackfun       = NULL;
void                     *g_adc_busy_irq_callbackfun_arg  = NULL;
pf_callback_fun_t        g_adc_timer_irq_callbackfun      = NULL;
void                     *g_adc_timer_irq_callbackfun_arg = NULL;
/*          From adc collect文件: api接口需求            */



/*          gpio设备通用接口定义                          */
static mcu_interface_t g_mcu_gpio =  
{
    .pfmcu_gpio_init       = MX_GPIO_Init,
    .pfmcu_gpio_deinit     = (void(*)(void*,uint16_t))HAL_GPIO_DeInit,
    .pfmcu_gpio_write_pin  = (void(*)(void*,uint16_t,gpio_pinstate_t))HAL_GPIO_WritePin,
    .pfmcu_gpio_read_pin   = (uint8_t(*)(void*,uint16_t))HAL_GPIO_ReadPin,
    .pfmcu_gpio_toggle_pin = (void(*)(void*,uint16_t))HAL_GPIO_TogglePin,
};
/*          gpio设备通用接口定义                          */



/*          From adc collect文件: api接口需求            */
//******************************** functions ********************************//

/*                    SPI操作api接口需求                 */
static mcu_spi_ret_code_t spi_dev_init(void)
{
    MX_SPI2_Init();
    return SPI_OK;
}

static mcu_spi_ret_code_t mcu_spi_cs_high(void)
{
    HAL_GPIO_WritePin(SPI2_CS_GPIO_Port, SPI2_CS_Pin, GPIO_PIN_SET);
    return SPI_OK;
}
static mcu_spi_ret_code_t mcu_spi_cs_low(void)
{
    HAL_GPIO_WritePin(SPI2_CS_GPIO_Port, SPI2_CS_Pin, GPIO_PIN_RESET);
    return SPI_OK;
}
/*                    SPI操作api接口需求                   */

/*                    TIMER操作api接口需求                 */
sys_adaption_ret_t timer_start_collect_data(void)
{
    if(HAL_OK != HAL_TIM_Base_Start_IT(&htim10))
    {
        SYSTEM_ADAPTION_LOG_ERROR("HAL_TIM_Base_Start_IT failed,file:%s,line:%d",__FILE__,__LINE__);
        return SYS_ADAPTION_ERROR;
    }
    return SYS_ADAPTION_OK;
}

sys_adaption_ret_t timer_stop_collect_data(void)
{
    if (HAL_OK != HAL_TIM_Base_Stop_IT(&htim10))
    {
        SYSTEM_ADAPTION_LOG_ERROR("HAL_TIM_Base_Stop_IT failed,file:%s,line:%d",__FILE__,__LINE__);
        return SYS_ADAPTION_ERROR;
    }
    return SYS_ADAPTION_OK;
}

/******************************************************************************
 * @brief 动态设置定时器频率
 *
 * @param htim      定时器句柄指针
 * @param frequency 目标频率值（单位：Hz）
 *
 * @return sys_adaption_ret_t 操作结果
 *         - SYS_ADAPTION_OK: 操作成功
 *         - SYS_ADAPTION_ERRORPARAMETER: 参数错误
 *         - SYS_ADAPTION_ERROR: 操作失败
 *****************************************************************************/
sys_adaption_ret_t Set_Timer_Frequency(TIM_HandleTypeDef* htim, uint32_t frequency)
{
    uint32_t timer_clock_freq;
    uint32_t prescaler;
    uint32_t period;
    uint32_t temp_calc;
    HAL_StatusTypeDef hal_status;

    /* 1. 参数有效性检查 */
    if (NULL == htim)
    {
        SYSTEM_ADAPTION_LOG_ERROR("Set_Timer_Frequency: htim is NULL, file:%s, line:%d", __FILE__, __LINE__);
        return SYS_ADAPTION_ERRORPARAMETER;
    }

    if (0 == frequency)
    {
        SYSTEM_ADAPTION_LOG_ERROR("Set_Timer_Frequency: frequency is 0, file:%s, line:%d", __FILE__, __LINE__);
        return SYS_ADAPTION_ERRORPARAMETER;
    }

    /* 2. 获取定时器时钟频率 */
    /* 根据系统配置，APB1定时器时钟频率为100MHz，APB2定时器时钟频率为100MHz */
    /* 简化检查，只检查当前项目中实际使用的定时器 */
    if ((htim->Instance == TIM2) || (htim->Instance == TIM3) || (htim->Instance == TIM4) ||
        (htim->Instance == TIM5))
    {
        /* APB1定时器 - 根据.ioc配置，APB1TimFreq_Value=100000000 */
        timer_clock_freq = 100000000UL;  /* 100MHz */
    }
    else if ((htim->Instance == TIM1)  || (htim->Instance == TIM9) ||
             (htim->Instance == TIM10) || (htim->Instance == TIM11))
    {
        /* APB2定时器 - 根据.ioc配置，APB2TimFreq_Value=100000000 */
        timer_clock_freq = 100000000UL;  /* 100MHz */
    }
    else
    {
        SYSTEM_ADAPTION_LOG_ERROR("Set_Timer_Frequency: unsupported timer instance, file:%s, line:%d", __FILE__, __LINE__);
        return SYS_ADAPTION_ERRORPARAMETER;
    }

    /* 3. 计算预分频器和周期值 */
    /* 定时器频率 = 定时器时钟频率 / ((预分频器 + 1) * (周期 + 1)) */
    /* 为了获得更好的精度，我们尝试使用较小的预分频器值 */

    /* 首先尝试预分频器为1 */
    prescaler = 0;  /* 预分频器寄存器值 = 实际分频值 - 1 */
    temp_calc = timer_clock_freq / frequency;

    if (temp_calc > 65536)  /* 16位定时器的最大周期值 */
    {
        /* 需要使用更大的预分频器 */
        prescaler = (temp_calc / 65536) + 1;
        if (prescaler > 65536)  /* 预分频器也是16位 */
        {
            SYSTEM_ADAPTION_LOG_ERROR("Set_Timer_Frequency: frequency too low, cannot achieve with 16-bit timer, file:%s, line:%d", __FILE__, __LINE__);
            return SYS_ADAPTION_ERRORPARAMETER;
        }
        prescaler -= 1;  /* 转换为寄存器值 */
        period = (timer_clock_freq / ((prescaler + 1) * frequency)) - 1;
    }
    else
    {
        period = temp_calc - 1;
    }

    /* 4. 检查计算结果的有效性 */
    if (period > 65535)
    {
        SYSTEM_ADAPTION_LOG_ERROR("Set_Timer_Frequency: calculated period exceeds 16-bit limit, file:%s, line:%d", __FILE__, __LINE__);
        return SYS_ADAPTION_ERRORPARAMETER;
    }

    SYSTEM_ADAPTION_LOG_INFO("Set_Timer_Frequency: target_freq=%lu Hz, prescaler=%lu, period=%lu\r\n",
                             frequency, prescaler, period);

    /* 5. 停止定时器 */
    hal_status = HAL_TIM_Base_Stop_IT(htim);
    if (HAL_OK != hal_status)
    {
        SYSTEM_ADAPTION_LOG_ERROR("Set_Timer_Frequency: HAL_TIM_Base_Stop_IT failed, status=%d, file:%s, line:%d",
                                  hal_status, __FILE__, __LINE__);
        return SYS_ADAPTION_ERROR;
    }

    /* 6. 更新定时器配置 */
    htim->Init.Prescaler = prescaler;
    htim->Init.Period = period;

    /* 7. 重新初始化定时器 */
    hal_status = HAL_TIM_Base_Init(htim);
    if (HAL_OK != hal_status)
    {
        SYSTEM_ADAPTION_LOG_ERROR("Set_Timer_Frequency: HAL_TIM_Base_Init failed, status=%d, file:%s, line:%d",
                                  hal_status, __FILE__, __LINE__);
        return SYS_ADAPTION_ERROR;
    }

    /* 8. 重新启动定时器 */
    hal_status = HAL_TIM_Base_Start_IT(htim);
    if (HAL_OK != hal_status)
    {
        SYSTEM_ADAPTION_LOG_ERROR("Set_Timer_Frequency: HAL_TIM_Base_Start_IT failed, status=%d, file:%s, line:%d",
                                  hal_status, __FILE__, __LINE__);
        return SYS_ADAPTION_ERROR;
    }

    SYSTEM_ADAPTION_LOG_INFO("Set_Timer_Frequency: successfully set timer frequency to %lu Hz\r\n", frequency);
    return SYS_ADAPTION_OK;
}

//*************************** data process api ******************************//

/*          From data process文件: api接口需求           */
#if CONFIG_USE_CURRENT_SENSOR
static sys_adaption_ret_t __find_arry_greater_indices( data_monitor_type_t *arry, 
                                                uint16_t size, 
                                                float value,
                                                uint16_t *index)
{
    /* 检查输入参数 */
    if((NULL == arry) || (NULL == index) || (0 == size))
    {
        return SYS_ADAPTION_ERRORRESOURCE;
    }
    /* 寻找大于value的最小索引 */
    uint16_t i = 0;
    for(i = 0; i < size; i++)
    {
        if(arry[i].data_monitor_limit > value)
        {
            *index = i;
            break;
        }
    }
    if(i == size)
    {
        return SYS_ADAPTION_ERROR;
    }
    return SYS_ADAPTION_OK;
}
/******************************************************************************
 * @brief Get the ctrl gain data object
 * 
 * @param  gain             
 * 
 * @return data_proc_ret_t 
 *****************************************************************************/
data_proc_ret_t get_ctrl_gain_data(float *gain)
{
    /* 1.检查输入参数 */
    if(NULL == gain)
    {
        SYSTEM_ADAPTION_LOG_DEUBG("get_ctrl_gain_data: gain input parameter error, \
            gain = %p, file = %s, line = %d", gain, __FILE__, __LINE__);
        return DATA_PROC_ERRORRESOURCE;
    }
    return DATA_PROC_OK;
}

sys_adaption_ret_t clear_adc_collect_data(adc_collect_xxx_set_event_t *pevent)
{
    if(NULL == pevent)
    {
        SYSTEM_ADAPTION_LOG_DEUBG("clear_adc_collect_data: pevent input parameter error, \
            pevent = %p, file = %s, line = %d", pevent, __FILE__, __LINE__);
        return SYS_ADAPTION_ERRORRESOURCE;
    }
    pevent->event_data = ADC_COLLECT_CLEAR_DATA_EVENT;
    if(ADC_COLLCET_OK != bsp_adc_collect_read_or_set(pevent))
    {
        SYSTEM_ADAPTION_LOG_DEUBG("clear_adc_collect_data: bsp_adc_collect_read_or_set error,\
                                file = %s, line = %d", __FILE__, __LINE__);
        return SYS_ADAPTION_ERROR;
    }
    return SYS_ADAPTION_OK;
}

/******************************************************************************
 * @brief 
 * 
 * @param  type             
 * @param  actual_amplitude 
 * 
 * @return data_proc_ret_t 
 *****************************************************************************/
data_proc_ret_t adjust_gain_data(data_proc_adjust_type_t type,
                                float actual_amplitude )
{
    SYSTEM_ADAPTION_LOG_DEUBG("adjust_gain_data start");
    uint16_t index = 0;
    sys_adaption_ret_t ret = SYS_ADAPTION_OK;
#if CONFIG_USE_CTR_AMP
    static ctrl_amp_gain_t last_gain_type = CTRL_AMP_GAIN_001X;
    bsp_ctrl_amp_event_t ctrl_amp_event;
#endif /* CONFIG_USE_CTR_AMP */
    adc_collect_xxx_set_event_t adc_collect_event;
    /* 1.根据type调整增益 */
    switch(type)
    {
        case DATA_PROC_ADJUST_CURRENT_GAIN :
        {
#if CONFIG_USE_CTR_AMP
            /* 2.1 电流部分使用增益 */
            ret = __find_arry_greater_indices(
                                            g_data_monitor_config_arry,     
                                            DATA_MONITOR_LIMIT_NUMS,
                                            actual_amplitude,
                                            &index);
            if(SYS_ADAPTION_OK != ret)
            {
                SYSTEM_ADAPTION_LOG_ERROR("adjust_gain_data: __find_arry_greater_indices error,file:%s,line:%d"
                                                                        ,__FILE__,__LINE__);
                return DATA_PROC_ERROR;
            }
            /* 2.2 采集范围需要调整的情况下 */
            if(g_adc_range_default != g_data_monitor_config_arry[index].adc_range)
            {
                /* 2.2.1 切换ADC采集范围 */
                adc_collect_event.event_data = ADC_COLLECT_SET_RANGE;
                adc_collect_event.range      = g_data_monitor_config_arry[index].adc_range;
                ctrl_amp_event.event_type    = CTRL_AMP_SET_GAIN_EVENT;
                ctrl_amp_event.set_gain_value = g_data_monitor_config_arry[index].ctrl_amp_gain;
                if(ADC_COLLCET_OK != bsp_adc_collect_read_or_set(&adc_collect_event))
                {
                    SYSTEM_ADAPTION_LOG_ERROR("adjust_gain_data: bsp_adc_collect_read_or_set error");
                    return DATA_PROC_ERROR;
                }
                /* 2.2.2 保存当前ADC采集范围 */
                g_adc_range_default = g_data_monitor_config_arry[index].adc_range;
                
                /* 2.2.4 清空ADC采集数据 */
                if(SYS_ADAPTION_OK != clear_adc_collect_data(&adc_collect_event))
                {
                    SYSTEM_ADAPTION_LOG_ERROR("adjust_gain_data: clear_adc_collect_data error");
                    return DATA_PROC_ERROR;
                }
#if CONFIG_USE_SLIDING_WINDOW
                /* 2.2.5 清空FFT输入数据 */
                if(FFT_CALCU_OK != fft_data_input_clear())
                {
                    SYSTEM_ADAPTION_LOG_ERROR("adjust_gain_data: fft_data_input_clear error");
                    return DATA_PROC_ERROR;
                }
#endif /* CONFIG_USE_SLIDING_WINDOW */
            }
                /* 2.3 采集范围不需要调整的情况下 */
            else
            {
                /* 2.3.1 检查增益是否需要调整 */
                if(last_gain_type != g_data_monitor_config_arry[index].ctrl_amp_gain)
                {
                    /* 2.3.1.2 保存当前增益 */
                    last_gain_type = g_data_monitor_config_arry[index].ctrl_amp_gain;
                    /* 2.3.1.3 清空ADC采集数据 */
                    if(SYS_ADAPTION_OK != clear_adc_collect_data(&adc_collect_event))
                    {
                        SYSTEM_ADAPTION_LOG_ERROR("adjust_gain_data: clear_adc_collect_data error");
                        return DATA_PROC_ERROR;
                    }
#if CONFIG_USE_SLIDING_WINDOW
                    /* 2.2.5 清空FFT输入数据 */
                    if(FFT_CALCU_OK != fft_data_input_clear())
                    {
                        SYSTEM_ADAPTION_LOG_ERROR("adjust_gain_data: fft_data_input_clear error");
                        return DATA_PROC_ERROR;
                    }
#endif /* CONFIG_USE_SLIDING_WINDOW */
                }
                else
                {
                    /* 2.3.2 无需调整增益 */
                    SYSTEM_ADAPTION_LOG_DEUBG("adjust_gain_data: no need adjust gain");
                }
            }
#endif /* CONFIG_USE_CTR_AMP */       
        }break;
        case DATA_PROC_ADJUST_VOLTAGE_GAIN :
        {
            /* 电压部分未使用增益，暂时不做处理 */
            SYSTEM_ADAPTION_LOG_DEUBG("adjust_gain_data: voltage gain not used");
        }break;
        default:
        {
            SYSTEM_ADAPTION_LOG_DEUBG("adjust_gain_data: type input parameter error, \
                type = %d, file = %s, line = %d", type, __FILE__, __LINE__);
            return DATA_PROC_ERROR;
        }
    }
    SYSTEM_ADAPTION_LOG_DEUBG("adjust_gain_data end");
    return DATA_PROC_OK;
}

data_proc_ret_t check_data_limit(float *data)
{
    /* 0.检查数据是否超出监测范围 */
    if(NULL == data)
    {
        SYSTEM_ADAPTION_LOG_DEUBG("check_data_limit: data input parameter error, \
            data = %p, file = %s, line = %d", data, __FILE__, __LINE__);
        return DATA_PROC_ERRORRESOURCE;
    }
    /* 根据ADC采集范围，确定监测限值 */
    switch(g_adc_range_default)
    {
        case ADC_COLLECT_RANGE_5V:
        {
            *data = DATA_LIMIT_SMALLER;
        }break;
        case ADC_COLLECT_RANGE_10V:
        {
            *data = DATA_LIMIT_LARGER;
        }break;
        default:
        {
            SYSTEM_ADAPTION_LOG_DEUBG("check_data_limit: g_adc_range_default input parameter error, \
                g_adc_range_default = %d, file = %s, line = %d", g_adc_range_default, __FILE__, __LINE__);
            return DATA_PROC_ERROR;
        };
    }
    return DATA_PROC_OK;
}
#endif
data_proc_ret_t write_modbus_data( data_proc_event_t data_event)
{
    rs485_reg_data_t reg_value;
    /* 1. 进行数据记录 */

    /* 2. 写入modbus数据 */
    switch(data_event.event_type)
    {
        case DATA_PROC_AC_VOLTAGE_DATA:
        {
            reg_value.f_data = data_event.event_data.ac_voltage_data.amplitude;
            if(WRITE_REG_SUCCESS != modbus_data_origin_input(AC_FUNDAMENTAL_VOLTAGE_REG,
                                                                            reg_value))
            {
                SYSTEM_ADAPTION_LOG_DEUBG("write_modbus_data:  current_amplitude datainput error");
                return DATA_PROC_ERROR;
            }
            reg_value.f_data = data_event.event_data.ac_voltage_data.phase;
            // ac_phase = data_event.event_data.ac_voltage_data.phase;
            if(WRITE_REG_SUCCESS!= modbus_data_origin_input(AC_FUNDAMENTAL_PHASE_REG,
                                                                            reg_value))
            {
                SYSTEM_ADAPTION_LOG_DEUBG("write_modbus_data:  current_phase datainput error");
                return DATA_PROC_ERROR;	
            }
            reg_value.f_data = data_event.event_data.ac_voltage_data.frequency;
            if(WRITE_REG_SUCCESS!= modbus_data_origin_input(AC_FUNDAMENTAL_FREQ_REG,
                                                                            reg_value))
            {
                SYSTEM_ADAPTION_LOG_DEUBG("write_modbus_data:  current_frequency datainput error");
                return DATA_PROC_ERROR;
            }
        }break;
        case DATA_PROC_UA_VOLTAGE_DATA:
        {
            reg_value.f_data = data_event.event_data.voltage_3ph_data.amplitude;
            modbus_data_origin_input(UA_FUNDAMENTAL_VOLTAGE_REG, reg_value);
            reg_value.f_data = data_event.event_data.voltage_3ph_data.phase;
            modbus_data_origin_input(UA_FUNDAMENTAL_PHASE_REG, reg_value);
            reg_value.f_data = data_event.event_data.voltage_3ph_data.amp_3rd_data;
            modbus_data_origin_input(UA_3RD_HARMONIC_VOLTAGE_REG, reg_value);
            reg_value.f_data = data_event.event_data.voltage_3ph_data.amp_5rd_data;
            modbus_data_origin_input(UA_5TH_HARMONIC_VOLTAGE_REG, reg_value);
            reg_value.f_data = data_event.event_data.voltage_3ph_data.amp_7rd_data;
            modbus_data_origin_input(UA_7TH_HARMONIC_VOLTAGE_REG, reg_value);
            reg_value.f_data = data_event.event_data.voltage_3ph_data.amp_9rd_data;
            modbus_data_origin_input(UA_9TH_HARMONIC_VOLTAGE_REG, reg_value);
        }break;
        case DATA_PROC_UB_VOLTAGE_DATA:
        {
            reg_value.f_data = data_event.event_data.voltage_3ph_data.amplitude;
            modbus_data_origin_input(UB_FUNDAMENTAL_VOLTAGE_REG, reg_value);
            reg_value.f_data = data_event.event_data.voltage_3ph_data.phase;
            modbus_data_origin_input(UB_FUNDAMENTAL_PHASE_REG, reg_value);
            reg_value.f_data = data_event.event_data.voltage_3ph_data.amp_3rd_data;
            modbus_data_origin_input(UB_3RD_HARMONIC_VOLTAGE_REG, reg_value);
            reg_value.f_data = data_event.event_data.voltage_3ph_data.amp_5rd_data;
            modbus_data_origin_input(UB_5TH_HARMONIC_VOLTAGE_REG, reg_value);
            reg_value.f_data = data_event.event_data.voltage_3ph_data.amp_7rd_data;
            modbus_data_origin_input(UB_7TH_HARMONIC_VOLTAGE_REG, reg_value);
            reg_value.f_data = data_event.event_data.voltage_3ph_data.amp_9rd_data;
            modbus_data_origin_input(UB_9TH_HARMONIC_VOLTAGE_REG, reg_value);
        }break;
        case DATA_PROC_UC_VOLTAGE_DATA:
        {
            reg_value.f_data = data_event.event_data.voltage_3ph_data.amplitude;
            modbus_data_origin_input(UC_FUNDAMENTAL_VOLTAGE_REG, reg_value);
            reg_value.f_data = data_event.event_data.voltage_3ph_data.phase;
            modbus_data_origin_input(UC_FUNDAMENTAL_PHASE_REG, reg_value);
            reg_value.f_data = data_event.event_data.voltage_3ph_data.amp_3rd_data;
            modbus_data_origin_input(UC_3RD_HARMONIC_VOLTAGE_REG, reg_value);
            reg_value.f_data = data_event.event_data.voltage_3ph_data.amp_5rd_data;
            modbus_data_origin_input(UC_5TH_HARMONIC_VOLTAGE_REG, reg_value);
            reg_value.f_data = data_event.event_data.voltage_3ph_data.amp_7rd_data;
            modbus_data_origin_input(UC_7TH_HARMONIC_VOLTAGE_REG, reg_value);
            reg_value.f_data = data_event.event_data.voltage_3ph_data.amp_9rd_data;
            modbus_data_origin_input(UC_9TH_HARMONIC_VOLTAGE_REG, reg_value);
        }break;
        default:
        {
            SYSTEM_ADAPTION_LOG_DEUBG("write_modbus_data: data_type input parameter error, \
                data_type = %d, file = %s, line = %d", data_type, __FILE__, __LINE__);
            return DATA_PROC_ERROR;
        }
    }
    return DATA_PROC_OK;
}

static sys_adaption_ret_t data_process_task_instance(void)
{
    SYSTEM_ADAPTION_LOG_DEUBG("data_process_task_instance start");
    /* 1.资源接口定义 */
    /* 1.1 RTOS队列接口 */
    static data_proc_handler_os_interface_t data_proc_os_interface = 
    {
        .rtos_queue_create  = (data_proc_os_ret_code_t(*)(void ** const, uint32_t, uint32_t))os_queue_create,
        .rtos_queue_delete  = (data_proc_os_ret_code_t(*)(void * const))os_queue_delete,
        .rtos_queue_send    = (data_proc_os_ret_code_t(*)(void * const, void * const, uint32_t))os_queue_send,
        .rtos_queue_receive = (data_proc_os_ret_code_t(*)(void * const, void * const, uint32_t))os_queue_receive,
    };
    /* 1.2 所需处理函数接口 */
    static data_process_function_t data_process_function = 
    {
        .func_flag           = DATA_PROC_FUNC_MODBUS_WRITE,
    #if CONFIG_USE_CURRENT_SENSOR
        .adjust_gain_data    = adjust_gain_data,
        .check_data_limit    = check_data_limit,
        .get_ctrl_gain_data  = get_ctrl_gain_data,
    #endif  /* CONFIG_USE_CURRENT_SENSOR */
        .write_modbus_data   = write_modbus_data,
    };
    static data_proc_system_config_t data_proc_system_config;
    data_proc_system_config.data_proc_handler_queue_size =
                        g_sys_adpation_queue_size.data_proc_handler_queue_size;
    static data_proc_input_data_t data_proc_input_data = 
    {
        .p_system_config     = &data_proc_system_config,
        .p_data_process_func = &data_process_function,
        .p_os_interface      = &data_proc_os_interface,
        .pf_init_cb          = data_proc_register_cb_init,
    };
    /* 2.创建数据处理任务 */
    if(pdTRUE != xTaskCreate(
                                data_proc_handler_thread,
                                "data_proc_handler_thread",
                                g_sys_adaption_task_stack.\
                                data_proc_handler_thread_stack_size,
                                &data_proc_input_data,
                                g_sys_adaption_task_priority.\
                                data_proc_handler_thread_priority,
                                NULL))
    {
        SYSTEM_ADAPTION_LOG_ERROR("data_process_task_instance: xTaskCreate error");
        return SYS_ADAPTION_ERROR;
    }
    SYSTEM_ADAPTION_LOG_DEUBG("data_process_task_instance end");
    return SYS_ADAPTION_OK;
}
/*          From data process文件: api接口需求           */
//*************************** data process api ******************************//

//*************************** fft calculate api *****************************//
/*          From fft calculate文件: api接口需求         */

/******************************************************************************
 * @brief 
 * 
 * @param  puser_data       
 * 
 *****************************************************************************/
void __read_adc_data_callback(void * const puser_data)
{
    SYSTEM_ADAPTION_LOG_DEUBG("__read_adc_data_callback start");
    fft_calcu_adc_arg_t *user_data = (fft_calcu_adc_arg_t *)puser_data;
    /* 1.检查输入参数 */
    if((NULL == puser_data))
    {
        SYSTEM_ADAPTION_LOG_DEUBG("__read_adc_data_callback: pdata input parameter error, \
                                file = %s, line = %d", __FILE__, __LINE__);
        return;
    }
    /* 2.获取ADC采集数据 */
    switch(user_data->data_type)
    {
        /* 2.1 电流部分 */
        case FFT_VOLTAGE_AC_DATA:
        {
            user_data->input_data[0].data_property = FFT_INPUT_VOLTAGE_AC;
            user_data->input_data[0].data_value = user_data->adc_data[0];
        }break;
        /* 2.2 交流电压部分 */
        case FFT_VOLTAGE_UA_DATA:
        {
            user_data->input_data[1].data_property = FFT_INPUT_VOLTAGE_UA;
            user_data->input_data[1].data_value = user_data->adc_data[1];
        }break;
        case FFT_VOLTAGE_UB_DATA:
        {
            user_data->input_data[1].data_property = FFT_INPUT_VOLTAGE_UB;
            user_data->input_data[1].data_value = user_data->adc_data[2];
        }break;
        case FFT_VOLTAGE_UC_DATA:
        {
            user_data->input_data[0].data_property = FFT_INPUT_VOLTAGE_UC;
            user_data->input_data[0].data_value = user_data->adc_data[3];
        }break;
        default:
        {
            SYSTEM_ADAPTION_LOG_DEUBG("__read_adc_data_callback: data_type input parameter error,\
                user_data = %d, file = %s, line = %d", user_data->data_type, __FILE__, __LINE__);
            return;
        }
    }
    SYSTEM_ADAPTION_LOG_DEUBG("__read_adc_data_callback end");
}

/******************************************************************************
 * @brief 
 * 
 * @param  input_data       
 * @param  data_type        
 * 
 * @return fft_calcu_ret_t 
 *****************************************************************************/
fft_calcu_ret_t fft_get_collect_data(fft_input_data_type_t * const input_data, 
                                     fft_data_type_t data_type)
{
    static fft_calcu_adc_arg_t fft_calcu_adc_arg;
    SYSTEM_ADAPTION_LOG_DEUBG("fft_get_collect_data start");
    /* 1.检查输入参数 */
    if(NULL == input_data)
    {
        SYSTEM_ADAPTION_LOG_DEUBG("fft_get_collect_data: input_data input parameter error, \
                                file = %s, line = %d", __FILE__, __LINE__);
        return FFT_CALCU_ERRORRESOURCE;
    }
    fft_calcu_adc_arg.input_data = input_data;
    fft_calcu_adc_arg.data_type  = data_type;
    /* 2.获取ADC采集数据 */
    adc_collect_xxx_output_event_t event;
    if(ADC_COLLCET_OK != bsp_adc_collect_output_data(&event))
    {
		vTaskDelay(5);
       SYSTEM_ADAPTION_LOG_DEUBG("fft_get_collect_data: bsp_adc_collect_output_data error");
       return FFT_CALCU_ERROR;
    }
    switch(data_type)
    {
        /* 所有ADC采集数据 */
        case FFT_ALL_DATA:
        {
            input_data[FFT_INPUT_VOLTAGE_AC].data_property = FFT_INPUT_VOLTAGE_AC;
            input_data[FFT_INPUT_VOLTAGE_AC].data_value    = event.data[1];
            input_data[FFT_INPUT_VOLTAGE_UA].data_property = FFT_INPUT_VOLTAGE_UA;
            input_data[FFT_INPUT_VOLTAGE_UA].data_value    = event.data[2];
            input_data[FFT_INPUT_VOLTAGE_UB].data_property = FFT_INPUT_VOLTAGE_UB;
            input_data[FFT_INPUT_VOLTAGE_UB].data_value    = event.data[3];
            input_data[FFT_INPUT_VOLTAGE_UC].data_property = FFT_INPUT_VOLTAGE_UC;
            input_data[FFT_INPUT_VOLTAGE_UC].data_value    = event.data[4];
        }break;
        /* 交流电压ADC采集数据 */
        case FFT_VOLTAGE_AC_DATA:
        {
            input_data[FFT_VOLTAGE_AC_DATA].data_property = FFT_INPUT_VOLTAGE_AC;
            input_data[FFT_VOLTAGE_AC_DATA].data_value    = event.data[1];
        }break;
        /* A相电压ADC采集数据 */
        case FFT_VOLTAGE_UA_DATA:
        {
            input_data[FFT_INPUT_VOLTAGE_UA].data_property = FFT_INPUT_VOLTAGE_UA;
            input_data[FFT_INPUT_VOLTAGE_UA].data_value    = event.data[2];
        }break;
        /* B相电压ADC采集数据 */
        case FFT_VOLTAGE_UB_DATA:
        {
            input_data[FFT_INPUT_VOLTAGE_UB].data_property = FFT_INPUT_VOLTAGE_UB;
            input_data[FFT_INPUT_VOLTAGE_UB].data_value    = event.data[3];
        }break;
        /* C相电压ADC采集数据 */
        case FFT_VOLTAGE_UC_DATA:
        {
            input_data[FFT_INPUT_VOLTAGE_UC].data_property = FFT_INPUT_VOLTAGE_UC;
            input_data[FFT_INPUT_VOLTAGE_UC].data_value    = event.data[4];
        }break;
        default:
        {
            SYSTEM_ADAPTION_LOG_DEUBG("fft_get_collect_data: data_type input parameter error, \
                data_type = %d, file = %s, line = %d", data_type, __FILE__, __LINE__);
            return FFT_CALCU_ERROR;
        }
    }
    SYSTEM_ADAPTION_LOG_DEUBG("fft_get_collect_data end");
    return FFT_CALCU_OK;
}
/******************************************************************************
 * @brief 
 * 
 * @param  datap_property   
 * @param  amplitude_data   
 * 
 * @return fft_calcu_ret_t 
 *****************************************************************************/
fft_calcu_ret_t fft_check_data_valid(fft_data_property_t datap_property, 
                                    float amplitude_data)
{
#if CONFIG_USE_CURRENT_SENSOR
    data_proc_event_t event;
    /* 1.根据数据属性，确定监测对象,均为电压数据 */
    if(datap_property >= FFT_INPUT_QUANTITY)
    {
        SYSTEM_ADAPTION_LOG_DEUBG("fft_check_data_valid: datap_property input \
                    parameter error, datap_property = %d, file = %s, line = %d", \
                                            datap_property, __FILE__, __LINE__);
        return FFT_CALCU_ERROR;
    }
    /* 2.发送数据到数据处理模块 */
    event.event_type = DATA_PROC_CHECK_VOLTAGE_DATA;
    event.event_data.check_voltage_data.collect_amplitude = amplitude_data;
    if(DATA_PROC_OK != data_process_event_send(&event))
    {
        SYSTEM_ADAPTION_LOG_DEUBG("fft_check_data_valid: data_process_event_send error");
        return FFT_CALCU_ERROR;
    }
#endif
    return FFT_CALCU_OK;
}

/******************************************************************************
 * @brief 
 * 
 * @param  data_limit       
 * 
 * @return fft_calcu_ret_t 
 *****************************************************************************/
fft_calcu_ret_t fft_get_collect_data_limit(float *const data_limit)
{

    /* 1.检查输入参数 */
    if(NULL == data_limit)
    {
        SYSTEM_ADAPTION_LOG_DEUBG("fft_get_collect_data_limit: data_limit input parameter error, \
                                file = %s, line = %d", __FILE__, __LINE__);
        return FFT_CALCU_ERRORRESOURCE;
    }
#if CONFIG_USE_CURRENT_SENSOR   
    /* 2.获取ADC采集限值 */
    data_proc_event_t event;
    event.event_type = DATA_PROC_GET_DATA_LIMIT_DATA;
    event.event_data.get_data_limit.data_limit = data_limit;
    if(DATA_PROC_OK != data_process_event_send(&event))
    {
        SYSTEM_ADAPTION_LOG_DEUBG("fft_get_collect_data_limit: data_process_event_send error");
        return FFT_CALCU_ERROR;
    }
#else
    *data_limit = DATA_LIMIT_LARGER;
#endif 
    return FFT_CALCU_OK;
}
/******************************************************************************
 * @brief 
 * 
 * 
 * @return fft_calcu_ret_t 
 *****************************************************************************/
fft_calcu_ret_t fft_result_data_send(fft_result_event_t *pevent)
{
    SYSTEM_ADAPTION_LOG_DEUBG("fft_result_data_send start");
    static float ac_phase_data = 0.0f;
    data_proc_event_t data_event;
     /* 1.检查输入参数 */
     if(NULL == pevent)
     {
         SYSTEM_ADAPTION_LOG_DEUBG("fft_result_data_send: pevent input parameter error, \
                                file = %s, line = %d", __FILE__, __LINE__);
         return FFT_CALCU_ERRORRESOURCE;
     }
     
    switch(pevent->data_property)
    {
        case FFT_INPUT_VOLTAGE_AC:
        {
            data_event.event_type = DATA_PROC_AC_VOLTAGE_DATA;
        }break;
        case FFT_INPUT_VOLTAGE_UA:
        {
            data_event.event_type = DATA_PROC_UA_VOLTAGE_DATA;
        }break;
        case FFT_INPUT_VOLTAGE_UB:
        {
            data_event.event_type = DATA_PROC_UB_VOLTAGE_DATA;
        }break;
        case FFT_INPUT_VOLTAGE_UC:
        {
            data_event.event_type = DATA_PROC_UC_VOLTAGE_DATA;
        }break;
        default:
        {
            SYSTEM_ADAPTION_LOG_DEUBG("fft_result_data_send: data_property input parameter error, \
                data_property = %d, file = %s, line = %d", pevent->data_property, __FILE__, __LINE__);
            return FFT_CALCU_ERROR;
        }
    }
    if(FFT_INPUT_VOLTAGE_AC == pevent->data_property)
     {
        data_event.event_data.ac_voltage_data.amplitude =
        pevent->fft_result_data.fundation_data.fft_result_amplitude_data;
        data_event.event_data.ac_voltage_data.phase     =
        pevent->fft_result_data.fundation_data.fft_result_phase_data;
        ac_phase_data = data_event.event_data.ac_voltage_data.phase;
        data_event.event_data.ac_voltage_data.frequency =
        pevent->fft_result_data.fundation_data.fft_result_freq_data;
     }
     else if((FFT_INPUT_VOLTAGE_UA == pevent->data_property) ||
             (FFT_INPUT_VOLTAGE_UB == pevent->data_property) ||
             (FFT_INPUT_VOLTAGE_UC == pevent->data_property))
     {
        data_event.event_data.voltage_3ph_data.amplitude = 
        pevent->fft_result_data.fundation_data.fft_result_amplitude_data;
        data_event.event_data.voltage_3ph_data.phase     =
        pevent->fft_result_data.fundation_data.fft_result_phase_data;
        data_event.event_data.voltage_3ph_data.amp_3rd_data =
        pevent->fft_result_data.three_phase_vol_data.fft_result_amp_3rd_data;
        data_event.event_data.voltage_3ph_data.amp_5rd_data =
        pevent->fft_result_data.three_phase_vol_data.fft_result_amp_5rd_data;
        data_event.event_data.voltage_3ph_data.amp_7rd_data =
        pevent->fft_result_data.three_phase_vol_data.fft_result_amp_7rd_data;
        data_event.event_data.voltage_3ph_data.amp_9rd_data =
        pevent->fft_result_data.three_phase_vol_data.fft_result_amp_9rd_data;
     }
     else
     {
        SYSTEM_ADAPTION_LOG_DEUBG("fft_result_data_send: data_property input parameter error, \
                data_property = %d, file = %s, line = %d", pevent->data_property, __FILE__, __LINE__);	
     }
    if(DATA_PROC_OK != data_process_event_send(&data_event))
    {
        SYSTEM_ADAPTION_LOG_ERROR("fft_result_data_send: data_process_event_send error");
        return FFT_CALCU_ERROR;
    }
    SYSTEM_ADAPTION_LOG_DEUBG("fft_result_data_send end");
    return FFT_CALCU_OK;
}
/*          From fft calculate文件: api接口需求         */
//*************************** fft calculate api *****************************//

/******************************************************************************
 * @brief fft线程接口实例化构造函数
 * 
 * 
 *****************************************************************************/
static sys_adaption_ret_t fft_calculate_task_instance(void)
{
    SYSTEM_ADAPTION_LOG_DEUBG("fft_calculate_task_instance start");
      /* 1.资源接口定义*/
      /* 1.1 fft数据处理信息接口 */
    static fft_data_property_t data_property[FFT_INPUT_QUANTITY] = 
    {
        FFT_INPUT_VOLTAGE_AC,
        FFT_INPUT_VOLTAGE_UA,
        FFT_INPUT_VOLTAGE_UB,
        FFT_INPUT_VOLTAGE_UC,
    };
    static fft_collect_msg_t fft_collect_msg = 
    {
        .fft_input_data_property = data_property,
        .fft_data_type           = FFT_ALL_DATA,
        .fft_calcu_type          = FFT_SYNC_CALCU_TYPE
    };
      /* 1.2 OS接口定义 */
    static fft_calcu_handler_os_interface_t fft_calcu_handler_os_interface =
    {
        .rtos_task_create           = (fft_calcu_os_ret_code_t(*)(task_function_t ,
                                                                  const char * const ,
                                                                  const uint16_t ,
                                                                  void * const ,
                                                                  uint32_t ,
                                                                  void ** const ))os_task_create,
        .rtos_task_get_handle       = (fft_calcu_os_ret_code_t(*)(void **const))  os_task_get_handle,
        .rtos_task_notifiy_take     = (fft_calcu_os_ret_code_t(*)(uint32_t))      os_task_notify_take,
        .rtos_task_notifiy_give     = (fft_calcu_os_ret_code_t(*)(void * const))  os_task_notify_give,
        .rtos_queue_create          = (fft_calcu_os_ret_code_t(*)(void ** const, uint32_t, uint32_t))os_queue_create,
        .rtos_queue_delete          = (fft_calcu_os_ret_code_t(*)(void * const))os_queue_delete,
        .rtos_queue_send            = (fft_calcu_os_ret_code_t(*)(void * const, void * const, uint32_t))os_queue_send,
        .rtos_queue_receive         = (fft_calcu_os_ret_code_t(*)(void * const, void * const, uint32_t))os_queue_receive,
        .rtos_semphorebinary_create = (fft_calcu_os_ret_code_t(*)(void ** const)) os_semaphore_binary_create,
        .rtos_semphore_delete       = (fft_calcu_os_ret_code_t(*)(void * const))os_semaphore_delete,
        .rtos_semphore_give_formisr = (fft_calcu_os_ret_code_t(*)(void * const))os_semaphore_give_fromisr,
        .rtos_semphore_take         = (fft_calcu_os_ret_code_t(*)(void * const, uint32_t))os_semaphore_take,
        .rtos_event_group_create    = (fft_calcu_os_ret_code_t(*)(void **const))os_event_group_create,
        .rtos_event_group_delete    = (fft_calcu_os_ret_code_t(*)(void *const))os_event_group_delete,
        .rtos_event_group_set_bits  = (fft_calcu_os_ret_code_t(*)(void *const, const uint32_t))os_event_group_set_bits,
        .rtos_event_group_clear_bits = (fft_calcu_os_ret_code_t(*)(void *const, const uint32_t))os_event_group_clear_bits,
        .rtos_event_group_wait_bits = (uint32_t(*)(void *const, const uint32_t, const uint8_t, const uint8_t, const uint32_t))os_event_group_wait_bits,
    };
      /* 1.3 系统时基接口定义 */
    static fft_calcu_system_timebase_interface_t fft_calcu_system_timebase_interface = 
    {
        .pfget_timetick_ms = HAL_GetTick,
    };
#ifdef FFT_CALCU_USING_IRQ
      /* 1.4 系统回调函数接口定义 */
    static fft_calcu_callback_fun_t fft_calcu_callback_fun = 
    {
        .pfirq_callbackfun = &gp_fft_irq_callbackfun,
        .params            = (void const **)&gp_fft_irq_callbackfun_arg
    };
#endif /* FFT_CALCU_USING_IRQ */
      /* 1.5 fft task所需接口定义 */
    static fft_calcu_get_data_interface_t fft_calcu_get_data_interface = 
    {
        .fft_get_collect_data       = fft_get_collect_data,
        .fft_check_data_valid       = fft_check_data_valid,
        .fft_get_collect_data_limit = fft_get_collect_data_limit,
        .fft_result_data_send       = fft_result_data_send,
    };
#ifdef FFT_CALCU_USING_IRQ
    static fft_calcu_timer_collect_t fft_calcu_timer_collect = 
    {
        .fft_start_timer_collect = timer_start_collect_data,
        .fft_stop_timer_collect  = timer_stop_collect_data,
    };
#endif /* FFT_CALCU_USING_IRQ */
    static fft_calcu_critical_t fft_calcu_critical = 
    {
        .rtos_enter_critical = (fft_calcu_os_ret_code_t(*)(void))os_critical_enter,
        .rtos_exit_critical  = (fft_calcu_os_ret_code_t(*)(void))os_critical_exit,
    };
    static fft_calcu_system_config_t fft_calcu_system_config;
    fft_calcu_system_config.fft_collection_data_thread_stack_size =
                    g_sys_adaption_task_stack.fft_collection_data_thread_stack_size;
    fft_calcu_system_config.fft_collection_data_thread_proirity =
                    g_sys_adaption_task_priority.fft_collection_data_thread_priority;
    fft_calcu_system_config.fft_finish_collection_queue_size =
                    g_sys_adpation_queue_size.fft_finish_collection_queue_size;
    /* 1.6 lpf_fir_t输入参数定义 */
    static lpf_fir_t lpf_fir_filter;
    if(LPF_FIR_OK != lpf_fir_instance(&lpf_fir_filter))
    {
        SYSTEM_ADAPTION_LOG_ERROR("fft_calculate_task_instance: \
        lpf_fir_instance error, file = %s, line = %d", __FILE__, __LINE__);
    }
    /* 1.7 fft task输入参数定义 */
    static fft_calcu_input_data_t fft_calcu_input_data = 
    {
        .fft_collect_msg                     = &fft_collect_msg,
        .fft_calcu_os_interface              = &fft_calcu_handler_os_interface,
        .fft_calcu_system_timebase_interface = &fft_calcu_system_timebase_interface,
#ifdef FFT_CALCU_USING_IRQ
        .fft_calcu_callback_fun              = &fft_calcu_callback_fun,
        .fft_timer_collect_interface         = &fft_calcu_timer_collect,
#endif /* FFT_CALCU_USING_IRQ */
        .fft_get_data_interface              = &fft_calcu_get_data_interface,
        .fft_calcu_critical_interface        = &fft_calcu_critical,
        .pfft_calcu_system_config            = &fft_calcu_system_config,
        .plpf_fir                            = &lpf_fir_filter,
    };
    /* 2.fft task创建 */
    if(pdTRUE != xTaskCreate(fft_event_handler_thread,
                             "fft_event_handler_thread", 
                            g_sys_adaption_task_stack.\
                            fft_event_handler_thread_stack_size, 
                            &fft_calcu_input_data, 
                            g_sys_adaption_task_priority.\
                            fft_event_handler_thread_priority,
                             NULL))
    {
        SYSTEM_ADAPTION_LOG_ERROR("fft_calculate_task_instance: fft_event_handler_thread create error,\
            file = %s, line = %d", __FILE__, __LINE__);
        return SYS_ADAPTION_ERROR;
    }
    SYSTEM_ADAPTION_LOG_DEUBG("fft_calculate_task_instance end");
    return SYS_ADAPTION_OK;
}
//*************************** fft calculate api *****************************//

//*************************** bsp_adc_collect_xxxx api **********************//

/* From Core层：     system_interrupt_interfac 函数构造     */
int8_t bsp_adc_collect_xxxx_interrupt_init(void)
{
	SYSTEM_ADAPTION_LOG_DEUBG("bsp_adc_collect_xxxxinterrupt_init start");
    __HAL_GPIO_EXTI_CLEAR_IT(AD7606_BUSY_Pin);
    HAL_NVIC_SetPriority(EXTI4_IRQn, 7, 0);
    HAL_NVIC_SetPriority(TIM1_UP_TIM10_IRQn, 7, 0);
    HAL_NVIC_SetPriority(DMA1_Stream4_IRQn, 5, 0);
    HAL_NVIC_SetPriority(SPI2_IRQn, 5, 0);
	SYSTEM_ADAPTION_LOG_DEUBG("bsp_adc_collect_xxxxinterrupt_init end");
    return 0;
}

int8_t bsp_adc_collect_xxxx_interrupt_deinit(void)
{
//    HAL_NVIC_DisableIRQ(EXTI4_IRQn);
//    HAL_NVIC_DisableIRQ(TIM1_UP_TIM10_IRQn);
//    __HAL_RCC_TIM10_CLK_DISABLE();
    return 0;
}

int8_t bsp_adc_collect_xxxx_interrupt_enable(void)
{
	SYSTEM_ADAPTION_LOG_DEUBG("bsp_adc_collect_xxxxinterrupt_enable start");
    HAL_NVIC_EnableIRQ(EXTI4_IRQn);
    HAL_NVIC_EnableIRQ(TIM1_UP_TIM10_IRQn);
	SYSTEM_ADAPTION_LOG_DEUBG("bsp_adc_collect_xxxxinterrupt_enable end");
    return 0;
}

int8_t bsp_adc_collect_xxxx_interrupt_disable(void)
{
//    HAL_NVIC_DisableIRQ(EXTI4_IRQn);
//    HAL_NVIC_DisableIRQ(TIM1_UP_TIM10_IRQn);
    return 0;
}

int8_t bsp_adc_collect_xxxx_enable_peripheral_clock(void)
{
    
    SYSTEM_ADAPTION_LOG_DEUBG("bsp_adc_collect_xxxxenable_peripheral_clock");
    __HAL_RCC_TIM10_CLK_ENABLE();
    return 0;
}

int8_t bsp_adc_collect_xxxx_disable_peripheral_clock(void)
{
    
//    SYSTEM_ADAPTION_LOG_DEUBG("bsp_adc_collect_xxxxdisable_peripheral_clock");
//    __HAL_RCC_TIM10_CLK_DISABLE();
    return 0;
}
/* From Core层：     system_interrupt_interfac 函数构造     */
/******************************************************************************
 * @brief 
 * 
 * @param  pspi_handler     
 * 
 * @return sys_adaption_ret_t 
 *****************************************************************************/
sys_adaption_ret_t ad7606_spi_dev_source_inst(spi_interface_t *pspi_handler)
{
    SYSTEM_ADAPTION_LOG_DEUBG("spi_dev_source_inst start");
    /* 0.检查输入参数 */
    if(NULL == pspi_handler)
    {
        SYSTEM_ADAPTION_LOG_DEUBG("spi_dev_source_inst: pspi_handler input parameter error, \
                                file = %s, line = %d", __FILE__, __LINE__);
        return SYS_ADAPTION_ERRORRESOURCE;
    }
    /* 1.资源接口定义*/
    spi_dev_ret_code_t ret;
    /* 1.1 动态内存分配接口定义 */
    static dynamic_allocation_t dynamic_allocation= 
    {
        .pfmalloc = pvPortMalloc,
        .pffree   = vPortFree
    };
    /* 1.2 spi设备信息接口定义 */
    static spi_handler_msg_t spi_dev_handler= 
    {
        .pspi_handle   = &hspi2,
        .cs_mode       = SPI_SOFT_CS,
        .callback_mode = SPI_RX_CALLBACK_EXTERNAL,
    }; 
    /* 1.3 spi设备软件CS接口定义 */
    static spi_soft_cs_t spi_soft_cs = 
    {
        .pfspi_soft_set_cs_high = mcu_spi_cs_high,
        .pfspi_soft_set_cs_low  = mcu_spi_cs_low,
    };
    /* 1.4 MCU的spi设备接口定义 */
    static mcu_spi_interface_t spi_interface= 
    {
        .pfspi_init      = spi_dev_init,
        .pfspi_deinit    = (mcu_spi_ret_code_t(*)(spi_handler_t*))HAL_SPI_DeInit,
        .pfspi_write     = (mcu_spi_ret_code_t(*)(spi_handler_t*, const uint8_t*, uint16_t, uint32_t))HAL_SPI_Transmit,
        .pfspi_read      = (mcu_spi_ret_code_t(*)(spi_handler_t*, uint8_t*, uint16_t, uint32_t))HAL_SPI_Receive,
		.pfspi_write_irq = (mcu_spi_ret_code_t(*)(spi_handler_t*, const uint8_t*, uint16_t))HAL_SPI_Transmit_IT,
        .pfspi_read_irq  = (mcu_spi_ret_code_t(*)(spi_handler_t*, uint8_t*, uint16_t))HAL_SPI_Receive_IT,
        .pfspi_write_dma = (mcu_spi_ret_code_t(*)(spi_handler_t*, const uint8_t*, uint16_t))HAL_SPI_Transmit_DMA,
        .pfspi_read_dma  = (mcu_spi_ret_code_t(*)(spi_handler_t*, uint8_t*, uint16_t))HAL_SPI_Receive_DMA,
    };
    /* 1.5 spi设备RTOS接口定义 */
    static spi_rtos_interface_t spi_rtos_interface = 
    {
        .rtos_semphorebinary_create = (spi_rtos_ret_code_t(*)(void ** const))os_semaphore_binary_create,
        .rtos_semphore_delete       = (spi_rtos_ret_code_t(*)(void * const))os_semaphore_delete,
        .rtos_semphore_give         = (spi_rtos_ret_code_t(*)(void * const))os_semaphore_give,
        .rtos_semphore_give_formisr = (spi_rtos_ret_code_t(*)(void * const))os_semaphore_give_fromisr,
        .rtos_semphore_take         = (spi_rtos_ret_code_t(*)(void * const, uint32_t))os_semaphore_take,
        .rtos_semphore_take_formisr = (spi_rtos_ret_code_t(*)(void * const))os_semaphore_take_fromisr,
        .rtos_timer_create          = (spi_rtos_ret_code_t(*)(const char * const, 
                                                              const uint32_t, 
                                                              void * const,
                                                              timercallbackfun_t,
                                                              void ** const))os_timer_create,
        .rtos_timer_delete          = (spi_rtos_ret_code_t(*)(void * const))os_timer_delete,
        .rtos_timer_start_fromisr   = (spi_rtos_ret_code_t(*)(void * const))os_timer_start_fromisr,
        .rtos_timer_stop_fromisr    = (spi_rtos_ret_code_t(*)(void * const))os_timer_stop_fromisr,
    };
    /* 1.6 spi设备回调函数接口定义 */
    static spi_callback_fun_t spi_callback= 
    {
        .txcallbackfun = &g_spi_txcallback,
        .rxcallbackfun = &g_spi_rxcallback,
    };
    /* 2.spi设备实例化 */
    static spi_device_t spi_device = {0};
    ret = spi_dev_instance(&dynamic_allocation, 
                           &spi_dev_handler,
                           &spi_interface, 
                           &spi_rtos_interface, 
                           &spi_callback,
                           &spi_soft_cs,
                           &spi_device);
    if(SPI_DEV_INST_OK != ret)
    {
        SYSTEM_ADAPTION_LOG_ERROR("spi_dev_instance failed,ret:%d, file:%s,line:%d",ret, __FILE__, __LINE__);
        return SYS_ADAPTION_ERROR;
    }
    /* 3.ad7606的spi接口挂载 */
    pspi_handler->pspi_handler                = (void*)&spi_device;
    pspi_handler->pfspi_dev_init              = (int8_t(*)(void*))spi_device.pfspi_dev_init;
    pspi_handler->pfspi_dev_deinit            = (int8_t(*)(void*))spi_device.pfspi_dev_deinit;
    pspi_handler->pfspi_dev_write             = (int8_t(*)(void*, uint8_t*, uint16_t, uint32_t))spi_device.pfspi_dev_write;
    pspi_handler->pfspi_dev_read              = (int8_t(*)(void*, uint8_t*, uint16_t, uint32_t))spi_device.pfspi_dev_read;
    pspi_handler->pfspi_dev_read_dma          = (int8_t(*)(void*, uint8_t*, uint16_t))spi_device.pfspi_dev_read_dma;
    pspi_handler->pfspi_dev_register_callback = (int8_t(*)(void*, pf_irq_callback_fun_t, pf_irq_callback_fun_t,void*))
                                                spi_device.pfspi_dev_register_callback;
    /* 4.检查ad7606的spi接口挂载是否成功 */
       if(
        (NULL    == pspi_handler->pspi_handler)     ||
        (NULL    == pspi_handler->pfspi_dev_init)   ||
        (NULL    == pspi_handler->pfspi_dev_deinit) ||
        (NULL    == pspi_handler->pfspi_dev_write)  ||
        (NULL    == pspi_handler->pfspi_dev_read)
       )
    SYSTEM_ADAPTION_LOG_DEUBG("spi_dev_instance ret:%d",ret);
    SYSTEM_ADAPTION_LOG_DEUBG("spi_dev_source_inst end");
    return SYS_ADAPTION_OK;
}

/******************************************************************************
 * @brief 
 * 
 * @param  p_gpio_interface 
 * 
 * @return sys_adaption_ret_t 
 *****************************************************************************/
static sys_adaption_ret_t ad7606_gpio_source_inst(gpio_interface_t *p_gpio_interface)
{
    SYSTEM_ADAPTION_LOG_DEUBG("ad7606_gpio_source_inst start");
    /* 0.检查输入参数 */
    if(NULL == p_gpio_interface)
    {
        SYSTEM_ADAPTION_LOG_DEUBG("ad7606_gpio_source_inst: p_gpio_interface input parameter error, \
                                file = %s, line = %d", __FILE__, __LINE__);
        return SYS_ADAPTION_ERRORRESOURCE;
    }
    /* 1.资源接口定义*/
    /* 1.1 MCU的GPIO接口定义 */
    /* 已定义为全局变量，直接使用 */
    /* 1.2 ad7606的GPIO接口定义 */
    static gpio_handler_t ad7606_gpio_msg[AD7606_GPIO_QUANTITY] = 
    {
        {
            .ps_mcu_interface = &g_mcu_gpio,
            .pgpio_port = AD7606_BUSY_GPIO_Port,
            .gpio_pin = AD7606_BUSY_Pin,
        },
        {
            .ps_mcu_interface = &g_mcu_gpio,
            .pgpio_port = AD7606_RESET_GPIO_Port,
            .gpio_pin = AD7606_RESET_Pin,
        },
        {
            .ps_mcu_interface = &g_mcu_gpio,
            .pgpio_port = AD7606_CONV_GPIO_Port,
            .gpio_pin = AD7606_CONV_Pin,
        },
        {
            .ps_mcu_interface = &g_mcu_gpio,
            .pgpio_port = AD7606_OS2_GPIO_Port,
            .gpio_pin = AD7606_OS2_Pin,
        },
        {
            .ps_mcu_interface = &g_mcu_gpio,
            .pgpio_port = AD7606_OS1_GPIO_Port,
            .gpio_pin = AD7606_OS1_Pin,
        },
        {
            .ps_mcu_interface = &g_mcu_gpio,
            .pgpio_port = AD7606_OS0_GPIO_Port,
            .gpio_pin = AD7606_OS0_Pin,
        },
        {
            .ps_mcu_interface = &g_mcu_gpio,
            .pgpio_port = AD7606_RANGE_GPIO_Port,
            .gpio_pin = AD7606_RANGE_Pin,
        },        

    };
    /* 2.ad7606的GPIO接口实例化 */
    static gpio_device_t ad7606_gpio_device[AD7606_CHANNEL_QUANTITY];
    for(int i = 0; i < AD7606_CHANNEL_QUANTITY; i++)
    {
        if(GPIO_DEV_INSTANCE_OK != gpio_dev_instance(&ad7606_gpio_device[i], &ad7606_gpio_msg[i]))
        {
            SYSTEM_ADAPTION_LOG_ERROR("ad7606 gpio device instance failed,file:%s,line:%d",__FILE__,__LINE__);
            return SYS_ADAPTION_ERROR;
        }
    }
    /* 3.ad7606的GPIO接口挂载 */
    for(int i = 0; i < AD7606_CHANNEL_QUANTITY; i++)
    {
        p_gpio_interface[i].pgpio_handler     = &ad7606_gpio_device[i];
        p_gpio_interface[i].pfgpio_init       = (int8_t(*)(void*))ad7606_gpio_device[i].pfgpio_init;
        p_gpio_interface[i].pfgpio_deinit     = (int8_t(*)(void*))ad7606_gpio_device[i].pfgpio_deinit;
        p_gpio_interface[i].pfgpio_write_pin  = (int8_t(*)(void*,gpio_interface_pinstate_t))ad7606_gpio_device[i].pfgpio_write_pin;
        p_gpio_interface[i].pfgpio_read_pin   = (gpio_interface_pinstate_t(*)(void*))ad7606_gpio_device[i].pfgpio_read_pin;
        p_gpio_interface[i].pfgpio_toggle_pin = (int8_t(*)(void*))ad7606_gpio_device[i].pfgpio_toggle_pin;
    };
    /* 4.检查ad7606的GPIO接口挂载是否成功 */
    for(int i = 0; i < AD7606_CHANNEL_QUANTITY; i++)
    {
        if(
            (NULL == p_gpio_interface[i].pgpio_handler)     ||
            (NULL == p_gpio_interface[i].pfgpio_init)       ||
            (NULL == p_gpio_interface[i].pfgpio_deinit)     ||
            (NULL == p_gpio_interface[i].pfgpio_write_pin)  ||
            (NULL == p_gpio_interface[i].pfgpio_read_pin)   ||
            (NULL == p_gpio_interface[i].pfgpio_toggle_pin)
        )
        {
            SYSTEM_ADAPTION_LOG_ERROR("ad7606 gpio device instance failed,file:%s,line:%d",__FILE__,__LINE__);
            return SYS_ADAPTION_ERROR;
        }
    }
    SYSTEM_ADAPTION_LOG_DEUBG("ad7606_gpio_source_inst end");
    return SYS_ADAPTION_OK;
}
/******************************************************************************
 * @brief 
 * 
 * 
 * @return sys_adaption_ret_t 
 *****************************************************************************/
static sys_adaption_ret_t adc_collect_handler_source_inst(void)
{
    /* 1.资源接口定义*/
	SYSTEM_ADAPTION_LOG_DEUBG("adc_collect_handler_source_inst start\r\n");
    /* 1.1 系统时间基接口定义 */
    static system_timebase_interface_t system_timebase_interface = 
    {
        .pfget_timetick_ms = HAL_GetTick,
    };
    /* 1.2 动态内存分配接口定义 */
    static ad7606_dynamic_allocation_t ad7606_dynamic_allocation = 
    {
        .pfmalloc = pvPortMalloc,
        .pffree   = vPortFree,
    };
    /* 1.3 系统RTOS接口定义 */
    static adc_collect_handler_os_interface_t os_interface = 
    {
        .rtos_task_create               = (adc_collect_handler_os_ret_code_t(*)(task_function_t ,
                                                                                const char * const ,
                                                                                const uint16_t ,
                                                                                void * const ,
                                                                                uint32_t ,
                                                                                void ** const ))os_task_create,
        .rtos_queue_create              = (adc_collect_handler_os_ret_code_t(*)(void ** const, uint32_t, uint32_t))os_queue_create,
        .rtos_queue_delete              = (adc_collect_handler_os_ret_code_t(*)(void * const))os_queue_delete,
        .rtos_queue_send                = (adc_collect_handler_os_ret_code_t(*)(void * const, void * const, uint32_t))os_queue_send,
        .rtos_queue_receive             = (adc_collect_handler_os_ret_code_t(*)(void * const, void * const, uint32_t))os_queue_receive,
        .rtos_semphorebinary_create     = (adc_collect_handler_os_ret_code_t(*)(void ** const))os_semaphore_binary_create,
        .rtos_mutex_create              = (adc_collect_handler_os_ret_code_t(*)(void ** const))os_mutex_create,
        .rtos_semphore_delete           = (adc_collect_handler_os_ret_code_t(*)(void * const))os_semaphore_delete,
        .rtos_semphore_give_formisr     = (adc_collect_handler_os_ret_code_t(*)(void * const))os_semaphore_give_fromisr,
        .rtos_semphore_give             = (adc_collect_handler_os_ret_code_t(*)(void * const))os_semaphore_give,   
        .rtos_semphore_take             = (adc_collect_handler_os_ret_code_t(*)(void * const, uint32_t))os_semaphore_take,
        .rtos_task_get_handle           = (adc_collect_handler_os_ret_code_t(*)(void ** const))os_task_get_handle,
        .rtos_task_notifiy_clear        = (adc_collect_handler_os_ret_code_t(*)(void * const))os_task_notify_clear,
        .rtos_task_notifiy_take         = (adc_collect_handler_os_ret_code_t(*)(uint32_t))os_task_notify_take,
        .rtos_task_notifiy_give_fromisr = (adc_collect_handler_os_ret_code_t(*)(void * const))os_task_notify_give_fromisr,
    };
    /* 1.4 输入参数接口定义 */
    static spi_interface_t ad7606_spi_device = {0};
    static gpio_interface_t ad7606_gpio_interface[AD7606_CHANNEL_QUANTITY];
    if(SYS_ADAPTION_OK != ad7606_spi_dev_source_inst(&ad7606_spi_device))
    {
        SYSTEM_ADAPTION_LOG_ERROR("ad7606_spi_dev_source_inst failed");
        return SYS_ADAPTION_ERROR;
    }
    if(SYS_ADAPTION_OK != ad7606_gpio_source_inst(ad7606_gpio_interface))
    {
        SYSTEM_ADAPTION_LOG_ERROR("ad7606_gpio_source_inst failed");
        return SYS_ADAPTION_ERROR;
    }
    static ad7606_busy_irq_callback_t ad7606_busy_irq_callback = 
    {
        .pf_busy_callback_fun = &g_adc_busy_irq_callbackfun,
        .p_arg                = &g_adc_busy_irq_callbackfun_arg
    };
    static ad7606_driver_input_t ad7606_driver_input = 
    {
        .pgpio_interface     = ad7606_gpio_interface,
        .pspi_interface      = &ad7606_spi_device,
        .pdynamic_allocation = &ad7606_dynamic_allocation,
        .pbusy_irq_callback  = &ad7606_busy_irq_callback
    };
    static system_interrupt_interface_t system_interrupt_interface =
    {
        .pfinit                     = bsp_adc_collect_xxxx_interrupt_init,
        .pfdeinit                   = bsp_adc_collect_xxxx_interrupt_deinit,
        .pfenable_irq               = bsp_adc_collect_xxxx_interrupt_enable,
        .pfenable_peripheral_clock  = bsp_adc_collect_xxxx_enable_peripheral_clock,
        .pfdisable_peripheral_clock = bsp_adc_collect_xxxx_disable_peripheral_clock,
    };
    static ring_buff_dynamic_allocation_t ring_buff_dynamic_allocation = 
    {
        .pfmalloc = pvPortMalloc,
        .pffree   = vPortFree,
    };
    static ring_buff_rtos_critical_t ring_buff_rtos_critical = 
    {
        .pf_rtos_critical_enter     = (ring_buff_rtos_ret_code_t(*)(void))os_critical_enter,
        .pf_rtos_critical_exit      = (ring_buff_rtos_ret_code_t(*)(void))os_critical_exit,
        .pf_rtos_critical_enter_isr = (ring_buff_rtos_ret_code_t(*)(uint32_t*))os_critical_enter_isr,
        .pf_rtos_critical_exit_isr  = (ring_buff_rtos_ret_code_t(*)(uint32_t))os_critical_exit_isr,
    };
    static adc_collect_callback_fun_t p_adc_collect_callback_fun = 
    {
        .timer_irq_callbackfun = &g_adc_timer_irq_callbackfun,
        .p_params              = (void const**)&g_adc_timer_irq_callbackfun_arg,
    };
    static adc_collect_timer_collect_t p_adc_collect_timer_collect = 
    {
        .pfstart_timer_collect = (adc_collect_handler_os_ret_code_t(*)(void))timer_start_collect_data,
        .pfstop_timer_collect  = (adc_collect_handler_os_ret_code_t(*)(void))timer_stop_collect_data,
    };
    static adc_collect_system_config_t system_config;
    system_config.adc_collect_set_event_thread_stack_size = 
                g_sys_adaption_task_stack.adc_collect_set_event_thread_stack_size;
    system_config.adc_collect_set_event_thread_proirity = 
                g_sys_adaption_task_priority.adc_collect_set_event_thread_priority;
    system_config.adc_collect_event_queue_size = 
                g_sys_adpation_queue_size.adc_collect_event_queue_size;
    system_config.adc_collect_set_event_queue_size = 
                g_sys_adpation_queue_size.adc_collect_set_event_queue_size;
    static adc_collect_handler_input_all_arg_t input_arg = 
    {
        .p_ad7606_driver_input              = &ad7606_driver_input,
        .p_system_interrupt_interface       = &system_interrupt_interface,
        .p_system_timebase_interface        = &system_timebase_interface,
        .pring_buff_dynamic_allocation      = &ring_buff_dynamic_allocation,
        .pring_buff_rtos_critical           = &ring_buff_rtos_critical,
        .p_adc_collect_handler_os_interface = &os_interface,
        .p_adc_collect_callback_fun         = &p_adc_collect_callback_fun,
        .p_adc_collect_timer_collect        = &p_adc_collect_timer_collect,
        .p_system_config                    = &system_config,
    };
    /* 2.创建任务 */
    if(pdTRUE !=xTaskCreate(adc_collect_event_thread, 
                            "adc_collect_event_thread", 
                            g_sys_adaption_task_stack.\
                            adc_collect_event_thread_stack_size, 
                            &input_arg, 
                            g_sys_adaption_task_priority.\
                            adc_collect_event_thread_priority, 
                            NULL))
	{
		SYSTEM_ADAPTION_LOG_ERROR("adc_collect_event_thread create failed");
        return SYS_ADAPTION_ERROR;
	}
	SYSTEM_ADAPTION_LOG_DEUBG("adc_collect_handler_source_inst end\r\n");
    return SYS_ADAPTION_OK;
}

SPI_TX_COMPLETE_CALLBACK_FUN
{
    if(NULL == g_spi_txcallback)
    {
        SYSTEM_ADAPTION_LOG_INFO("g_spi_txcallback is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return;
    }
    g_spi_txcallback(hspi);
}

SPI_RX_COMPLETE_CALLBACK_FUN
{
//    static uint32_t spi_enter_count = 0;
    if(NULL == g_spi_rxcallback)
    {
        SYSTEM_ADAPTION_LOG_INFO("g_spi_rxcallback is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return;
    }
     g_spi_rxcallback(hspi);
//	spi_enter_count++;
}

//*************************** bsp_adc_collect_xxxx api **********************//




//*************************** rs485_event_handler api ***********************//


//*************************** rs485_event_handler api ***********************//



sys_adaption_ret_t uart_dev_register(void)
{
    SYSTEM_ADAPTION_LOG_DEUBG("uart_dev_register start");
    /* 1.uart设备接口定义 */
    static uart_msg_t stm32_uart_msg =
    {
        .ps_uart[uart_port2] = &huart2,
    };
	if(0 != register_uart_funtion(stm32_uart_msg, 
                         MX_USART2_UART_Init,
                         NULL, 
                         HAL_UART_Transmit,
                         HAL_UART_Transmit_IT,
                         HAL_UART_Receive,
                         HAL_UART_Receive_IT,
                         HAL_UARTEx_ReceiveToIdle_IT))
    {
        SYSTEM_ADAPTION_LOG_ERROR("register_uart_funtion failed");
        return SYS_ADAPTION_ERROR;
    }
    SYSTEM_ADAPTION_LOG_DEUBG("uart_dev_register end");
    return SYS_ADAPTION_OK;
}

sys_adaption_ret_t rs485_reg_init(void)
{
    SYSTEM_ADAPTION_LOG_DEUBG("rs485_reg_init start");
    rs485_reg_t reg_msg;

    // 基波相关寄存器
    // 交流基波电压
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = AC_FUNDAMENTAL_VOLTAGE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(AC_FUNDAMENTAL_VOLTAGE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg AC_FUNDAMENTAL_VOLTAGE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // 交流基波相位
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = AC_FUNDAMENTAL_PHASE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(AC_FUNDAMENTAL_PHASE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg AC_FUNDAMENTAL_PHASE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // 交流基波频率
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = AC_FUNDAMENTAL_FREQ_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(AC_FUNDAMENTAL_FREQ_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg AC_FUNDAMENTAL_FREQ_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UA相基波及谐波寄存器
    // UA相基波电压
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UA_FUNDAMENTAL_VOLTAGE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UA_FUNDAMENTAL_VOLTAGE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UA_FUNDAMENTAL_VOLTAGE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UA相基波相位
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UA_FUNDAMENTAL_PHASE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UA_FUNDAMENTAL_PHASE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UA_FUNDAMENTAL_PHASE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UA相3次谐波电压
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UA_3RD_HARMONIC_VOLTAGE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UA_3RD_HARMONIC_VOLTAGE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UA_3RD_HARMONIC_VOLTAGE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UA相5次谐波电压
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UA_5TH_HARMONIC_VOLTAGE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UA_5TH_HARMONIC_VOLTAGE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UA_5TH_HARMONIC_VOLTAGE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UA相7次谐波电压
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UA_7TH_HARMONIC_VOLTAGE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UA_7TH_HARMONIC_VOLTAGE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UA_7TH_HARMONIC_VOLTAGE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UA相9次谐波电压
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UA_9TH_HARMONIC_VOLTAGE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UA_9TH_HARMONIC_VOLTAGE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UA_9TH_HARMONIC_VOLTAGE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UB相基波及谐波寄存器
    // UB相基波电压
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UB_FUNDAMENTAL_VOLTAGE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UB_FUNDAMENTAL_VOLTAGE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UB_FUNDAMENTAL_VOLTAGE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UB相基波相位
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UB_FUNDAMENTAL_PHASE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UB_FUNDAMENTAL_PHASE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UB_FUNDAMENTAL_PHASE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UB相3次谐波电压
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UB_3RD_HARMONIC_VOLTAGE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UB_3RD_HARMONIC_VOLTAGE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UB_3RD_HARMONIC_VOLTAGE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UB相5次谐波电压
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UB_5TH_HARMONIC_VOLTAGE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UB_5TH_HARMONIC_VOLTAGE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UB_5TH_HARMONIC_VOLTAGE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UB相7次谐波电压
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UB_7TH_HARMONIC_VOLTAGE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UB_7TH_HARMONIC_VOLTAGE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UB_7TH_HARMONIC_VOLTAGE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UB相9次谐波电压
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UB_9TH_HARMONIC_VOLTAGE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UB_9TH_HARMONIC_VOLTAGE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UB_9TH_HARMONIC_VOLTAGE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UC相基波及谐波寄存器
    // UC相基波电压
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UC_FUNDAMENTAL_VOLTAGE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UC_FUNDAMENTAL_VOLTAGE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UC_FUNDAMENTAL_VOLTAGE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UC相基波相位
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UC_FUNDAMENTAL_PHASE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UC_FUNDAMENTAL_PHASE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UC_FUNDAMENTAL_PHASE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UC相3次谐波电压
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UC_3RD_HARMONIC_VOLTAGE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UC_3RD_HARMONIC_VOLTAGE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UC_3RD_HARMONIC_VOLTAGE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UC相5次谐波电压
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UC_5TH_HARMONIC_VOLTAGE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UC_5TH_HARMONIC_VOLTAGE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UC_5TH_HARMONIC_VOLTAGE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UC相7次谐波电压
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UC_7TH_HARMONIC_VOLTAGE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UC_7TH_HARMONIC_VOLTAGE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UC_7TH_HARMONIC_VOLTAGE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // UC相9次谐波电压
    reg_msg.data_bit = REG_FLOAT;
    reg_msg.property = READ_ONLY;
    reg_msg.reg_stru.reg_num = UC_9TH_HARMONIC_VOLTAGE_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 2;
    reg_msg.reg_data.f_data = 0.0f;
    if(0 != rs485_instance_reg_msg(UC_9TH_HARMONIC_VOLTAGE_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg UC_9TH_HARMONIC_VOLTAGE_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    // 设备地址寄存器
    reg_msg.data_bit = RGE_8BIT;
    reg_msg.property = READABLE_WRITEABLE;
    reg_msg.reg_stru.reg_num = DEV_ADR_REG_ADRESS;
    reg_msg.reg_stru.reg_len = 1;
    reg_msg.reg_data.u8_data = 1;
    if(0 != rs485_instance_reg_msg(DEV_ADR_REG, reg_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_instance_reg_msg DEV_ADR_REG failed");
        return SYS_ADAPTION_ERROR;
    }

    SYSTEM_ADAPTION_LOG_DEUBG("rs485_reg_init end");
    return SYS_ADAPTION_OK;
}
 /* 保存RS485寄存器值到flash */
    rs485_handler_ret_t flash_save_rs485_reg_value(uint16_t reg_place, rs485_reg_data_t* reg_value)
    {
        if (NULL == reg_value)
        {
            SYSTEM_ADAPTION_LOG_ERROR("flash_save_rs485_reg_value: reg_value is NULL, file: %s, line: %d", __FILE__, __LINE__);
            return RS485_HANDLER_ERRORPARAMETER;
        }
        
        flash_event_t flash_event;
        
        /* 根据寄存器类型设置对应的flash事件 */
        switch (reg_place)
        {
            case DEV_ADR_REG:
            {
                flash_event.type = SAVE_DEV_ADR_EVENT;
                flash_event.dev_adr = (uint8_t)reg_value->u8_data;
                
                if (FLASH_HANDLER_OK != flash_event_send(&flash_event))
                {
                    SYSTEM_ADAPTION_LOG_ERROR("flash_save_rs485_reg_value: save device address failed, file: %s, line: %d", __FILE__, __LINE__);
                    return RS485_HANDLER_ERROR;
                }
            }
            break;
            
            /* 可以根据需要添加其他寄存器的保存处理 */
            
            default:
                SYSTEM_ADAPTION_LOG_ERROR("flash_save_rs485_reg_value: unsupported register place %d, file: %s, line: %d", reg_place, __FILE__, __LINE__);
                return RS485_HANDLER_ERROR;
        }
        
        return RS485_HANDLER_OK;
    }
    
    /* 从flash读取RS485寄存器值 */
    rs485_handler_ret_t flash_read_rs485_reg_value(uint16_t reg_place, rs485_reg_data_t* reg_value)
    {
        if (NULL == reg_value)
        {
            SYSTEM_ADAPTION_LOG_ERROR("flash_read_rs485_reg_value: reg_value is NULL, file: %s, line: %d", __FILE__, __LINE__);
            return RS485_HANDLER_ERRORPARAMETER;
        }
        
        /* 读取系统信息 */
        system_info_t sys_info;
        flash_event_t flash_event;
        
        flash_event.type = READ_SYSTEM_INFO_EVENT;
        flash_event.p_sys_info = &sys_info;
        
        if (FLASH_HANDLER_OK != flash_event_send(&flash_event))
        {
            SYSTEM_ADAPTION_LOG_ERROR("flash_read_rs485_reg_value: read system info failed, file: %s, line: %d", __FILE__, __LINE__);
            return RS485_HANDLER_ERROR;
        }
        
        /* 根据寄存器类型获取对应的值 */
        switch (reg_place)
        {
            case DEV_ADR_REG:
            {
                reg_value->u8_data = (uint8_t)sys_info.dev_adr;
            }
            break;
            
            /* 可以根据需要添加其他寄存器的读取处理 */
            
            default:
                SYSTEM_ADAPTION_LOG_ERROR("flash_read_rs485_reg_value: unsupported register place %d, file: %s, line: %d", reg_place, __FILE__, __LINE__);
                return RS485_HANDLER_ERROR;
        }
        
        return RS485_HANDLER_OK;
    }

sys_adaption_ret_t rs485_event_handler_inst(void)
{
    SYSTEM_ADAPTION_LOG_DEUBG("rs485_event_handler_inst start");
    
    /* 1. 资源接口定义 */
    /* 1.0 系统信息配置 */
    static rs485_event_system_config_t rs485_event_system_config;
    rs485_event_system_config.rs485_event_thread_stack_size =
            g_sys_adaption_task_stack.rs485_event_handle_thread_stack_size;
    rs485_event_system_config.rs485_event_thread_proirity =
            g_sys_adaption_task_priority.rs485_event_handle_thread_priority;
    rs485_event_system_config.rs485_output_msg_thread_stack_size =
            g_sys_adaption_task_stack.rs485_output_msg_thread_stack_size;
    rs485_event_system_config.rs485_output_msg_thread_proirity =
            g_sys_adaption_task_priority.rs485_output_msg_thread_priority;
    rs485_event_system_config.rs485_event_queue_size =
                        g_sys_adpation_queue_size.rs485_event_queue_size;
    
    /* 1.1 RTOS接口定义 */
    static rs485_rtos_interface_t rs485_rtos_interface = {
        .rtos_queue_create  = (rs485_os_ret_t(*)(void**, uint32_t, uint32_t))os_queue_create,
        .rtos_task_create   = (rs485_os_ret_t(*)(void(*)(void*), const char*, uint32_t, void*, uint32_t, void**))os_task_create,
        .rtos_queue_delete  = (rs485_os_ret_t(*)(void*))os_queue_delete,
        .rtos_task_delete   = (rs485_os_ret_t(*)(void*))os_task_delete,
        .rtos_queue_send    = (rs485_os_ret_t(*)(void*, void*, uint32_t))os_queue_send,
        .rtos_queue_receive = (rs485_os_ret_t(*)(void*, void*, uint32_t))os_queue_receive,
        .rtos_timer_create  = (void*(*)(const char*, uint32_t, bool, void*, void(*)(void*)))os_timer_create,
        .rtos_timer_start   = (rs485_os_ret_t(*)(void*, uint32_t))os_timer_start_fromisr,
        .rtos_timer_stop    = (rs485_os_ret_t(*)(void*, uint32_t))os_timer_stop_fromisr,
        .rtos_timer_delete  = (rs485_os_ret_t(*)(void*, uint32_t))os_timer_delete
    };
    
    /* 1.2 RS485操作函数接口定义 */
    static rs485_ope_fun_t rs485_ope_functions = 
    {
        .rs485_save_reg_value = flash_save_rs485_reg_value,
        .rs485_read_reg_value = flash_read_rs485_reg_value
    };
    
    /* 1.3 GPIO接口初始化 */
    static gpio_handler_t rs485_de_msg = {
        .ps_mcu_interface = &g_mcu_gpio,
        .pgpio_port = RS485_DE_GPIO_Port,
        .gpio_pin = RS485_DE_Pin,
    };
    static gpio_device_t rs485_de_gpio;
    if(GPIO_DEV_INSTANCE_OK != gpio_dev_instance(&rs485_de_gpio, &rs485_de_msg))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_de_gpio device instance failed");
        return SYS_ADAPTION_ERROR;
    }
    if(RS485_DEV_OK != add_gpio_to_rs485(&rs485_de_gpio))
    {
        SYSTEM_ADAPTION_LOG_ERROR("add_gpio_to_rs485 failed");
        return SYS_ADAPTION_ERROR;
    }
    
    /* 2. UART设备注册 */
    if(SYS_ADAPTION_OK != uart_dev_register())
    {
        SYSTEM_ADAPTION_LOG_ERROR("uart_dev_register failed");
        return SYS_ADAPTION_ERROR;
    }
    
    /* 3. RS485寄存器初始化 */
    if(SYS_ADAPTION_OK != rs485_reg_init())
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_reg_init failed");
        return SYS_ADAPTION_ERROR;
    }
    
    /* 4. 构造RS485事件处理器输入参数 */
    static rs485_handler_input_t rs485_handler_input = {
        .p_sys_config   = &rs485_event_system_config,
        .p_os_interface = &rs485_rtos_interface,
        .p_ope_fun      = &rs485_ope_functions
    };
    
    /* 5. RS485事件处理器初始化 */
    if(RS485_HANDLER_OK != rs485_event_handler_init(&rs485_handler_input))
    {
        SYSTEM_ADAPTION_LOG_ERROR("rs485_event_handler_init failed");
        return SYS_ADAPTION_ERROR;
    }
    
    SYSTEM_ADAPTION_LOG_DEUBG("rs485_event_handler_inst end");
    return SYS_ADAPTION_OK;
}
//*************************** rs485_event_handler api ***********************//

//*************************** flash_event_handler api ***********************//

sys_adaption_ret_t flash_event_handler_inst(void)
{
    SYSTEM_ADAPTION_LOG_DEUBG("flash_event_handler_inst start");
    static flash_event_system_config_t flash_event_system_config;
    flash_event_system_config.flash_event_thread_stack_size = 
                g_sys_adaption_task_stack.flash_event_handler_thread_stack_size;
    flash_event_system_config.flash_event_thread_proirity   = 
                g_sys_adaption_task_priority.flash_event_handler_thread_priority;  
    flash_event_system_config.flash_event_queue_size        = 
                g_sys_adpation_queue_size.flash_event_queue_size;
    
    // 添加RTOS操作接口
    static flash_os_interface_t flash_os_interface = {
        .rtos_queue_create  = (flash_rtos_result_t(*)(void**, uint16_t, uint16_t))os_queue_create,
        .rtos_queue_delete  = (flash_rtos_result_t(*)(void*))os_queue_delete,
        .rtos_queue_send    = (flash_rtos_result_t(*)(void*, const void*, uint32_t))os_queue_send,
        .rtos_queue_receive = (flash_rtos_result_t(*)(void*, void*, uint32_t))os_queue_receive,
        .rtos_task_create   = (flash_rtos_result_t(*)(void*, const char*, uint16_t, void*, uint32_t, void**))os_task_create
    };
    
    static flash_event_input_t flash_handler_input = 
    {   
        .p_config               = &flash_event_system_config,
        .pos_interface          = &flash_os_interface,
        .pf_flash_event_cb_init = flash_event_register_cb_init,
    };
    
    if(FLASH_HANDLER_OK != flash_event_handler_init(&flash_handler_input))
    {
        SYSTEM_ADAPTION_LOG_ERROR("flash_event_handler_init failed");
        return SYS_ADAPTION_ERROR;
    }
    SYSTEM_ADAPTION_LOG_DEUBG("flash_event_handler_inst end");
    return SYS_ADAPTION_OK;
}

//*************************** flash_event_handler api ***********************//

//*************************** led_handler api ********************************//

/* LED处理器任务函数声明 */
extern void led_handler_task(void *argument);

/* 全局LED handler实例 */
static bsp_led_handler_t g_led_handler;
/******************************************************************************
 * @brief LED GPIO接口实例化
 *
 * @param p_gpio_interface GPIO接口指针
 * @param gpio_port GPIO端口
 * @param gpio_pin GPIO引脚
 * @return sys_adaption_ret_t
 *****************************************************************************/
static sys_adaption_ret_t led_gpio_interface_inst(led_gpio_interface_t *p_gpio_interface,
                                                   gpio_device_t *p_gpio_device,
                                                   gpio_handler_t *p_gpio_handler,
                                                   void *gpio_port,
                                                   uint16_t gpio_pin)
{
    if (p_gpio_interface == NULL) {
        return SYS_ADAPTION_ERRORPARAMETER;
    }

    /* 创建GPIO设备 */
    p_gpio_handler->ps_mcu_interface = &g_mcu_gpio;
    p_gpio_handler->pgpio_port = gpio_port;
    p_gpio_handler->gpio_pin = gpio_pin;

    if(GPIO_DEV_INSTANCE_OK != gpio_dev_instance(p_gpio_device, p_gpio_handler))
    {
        SYSTEM_ADAPTION_LOG_ERROR("led gpio device instance failed");
        return SYS_ADAPTION_ERROR;
    }

    /* 设置LED GPIO接口 */
    p_gpio_interface->pgpio_handler = p_gpio_device;
    p_gpio_interface->pfgpio_init = (int8_t(*)(void*))p_gpio_device->pfgpio_init;
    p_gpio_interface->pfgpio_deinit = (int8_t(*)(void*))p_gpio_device->pfgpio_deinit;
    p_gpio_interface->pfgpio_write_pin = (int8_t(*)(void*, led_gpio_interface_state_t))p_gpio_device->pfgpio_write_pin;
    p_gpio_interface->pfgpio_read_pin = (led_gpio_interface_state_t(*)(void*))p_gpio_device->pfgpio_read_pin;
    p_gpio_interface->pfgpio_toggle_pin = (int8_t(*)(void*))p_gpio_device->pfgpio_toggle_pin;

    return SYS_ADAPTION_OK;
}

/******************************************************************************
 * @brief LED驱动实例化
 *
 * @param p_led_driver LED驱动指针
 * @param led_which LED编号
 * @param gpio_port GPIO端口
 * @param gpio_pin GPIO引脚
 * @return sys_adaption_ret_t
 *****************************************************************************/
static sys_adaption_ret_t led_driver_inst(bsp_led_driver_t *p_led_driver,
                                          led_gpio_interface_t *p_gpio_interface,
                                          led_which_t led_which,
                                          void *gpio_port,
                                          uint16_t gpio_pin)
{
    if (p_led_driver == NULL || p_gpio_interface == NULL) {
        return SYS_ADAPTION_ERRORPARAMETER;
    }
#ifdef USE_DYNAMIC_ALLOCATION
    gpio_device_t *pled_gpio_device = (gpio_device_t *)pvPortMalloc(sizeof(gpio_device_t));
    gpio_handler_t *pled_gpio_handler = (gpio_handler_t *)pvPortMalloc(sizeof(gpio_handler_t));
#else
    static gpio_device_t led_gpio_device[LED_DEV_Quantity];
    static gpio_handler_t led_gpio_handler[LED_DEV_Quantity];
#endif
    /* 1. GPIO接口实例化 */
    if (SYS_ADAPTION_OK != led_gpio_interface_inst(p_gpio_interface, 
#ifdef USE_DYNAMIC_ALLOCATION
                                                   pled_gpio_device,
                                                   pled_gpio_handler,
#else
                                                   &led_gpio_device[led_which],
                                                   &led_gpio_handler[led_which],
#endif
                                                   gpio_port, gpio_pin)) 
    {
        SYSTEM_ADAPTION_LOG_ERROR("led_gpio_interface_inst failed");
        return SYS_ADAPTION_ERROR;
    }

    /* 2. 动态分配接口 */
    static led_dynamic_allocation_t led_dynamic_allocation = {
        .pfmalloc = pvPortMalloc,
        .pffree   = vPortFree,
    };

    /* 3. LED驱动实例化 */
    if (LED_DRIVER_OK != bsp_led_driver_inst(p_led_driver,
                                             p_gpio_interface,
                                             &led_dynamic_allocation,
                                             led_which)) {
        SYSTEM_ADAPTION_LOG_ERROR("bsp_led_driver_inst failed for LED %d", led_which);
        return SYS_ADAPTION_ERROR;
    }

    return SYS_ADAPTION_OK;
}

static sys_adaption_ret_t led_handler_inst(void)
{
    SYSTEM_ADAPTION_LOG_DEUBG("led_handler_inst start");

    /* 1. 系统配置 */
    static led_handler_system_config_t led_handler_system_config;
    led_handler_system_config.led_handler_task_priority =
                g_sys_adaption_task_priority.led_handler_thread_priority;
    led_handler_system_config.led_handler_task_stack_size =
                g_sys_adaption_task_stack.led_handler_thread_stack_size;
    led_handler_system_config.led_handler_event_queue_size =
                g_sys_adpation_queue_size.led_handler_event_queue_size;

    /* 2. RTOS接口定义 */
    static led_handler_os_interface_t led_handler_os_interface = 
    {
        .rtos_mutex_create       = (led_handler_rtos_ret_code_t(*)(void**))os_mutex_create,
        .rtos_mutex_delete       = (led_handler_rtos_ret_code_t(*)(void*))os_semaphore_delete,
        .rtos_mutex_take         = (led_handler_rtos_ret_code_t(*)(void*, uint32_t))os_semaphore_take,
        .rtos_mutex_give         = (led_handler_rtos_ret_code_t(*)(void*))os_semaphore_give,
        .rtos_task_create        = (led_handler_rtos_ret_code_t(*)(task_function_t, const char*, uint16_t, void*, uint32_t, void**))os_task_create,
        .rtos_task_delete        = (led_handler_rtos_ret_code_t(*)(void*))os_task_delete,
        .rtos_queue_create       = (led_handler_rtos_ret_code_t(*)(void**, uint32_t, uint32_t))os_queue_create,
        .rtos_queue_delete       = (led_handler_rtos_ret_code_t(*)(void*))os_queue_delete,
        .rtos_queue_send         = (led_handler_rtos_ret_code_t(*)(void*, void*, uint32_t))os_queue_send,
        .rtos_queue_send_fromisr = (led_handler_rtos_ret_code_t(*)(void*, void*, void*))os_queue_send_fromisr,
        .rtos_queue_receive      = (led_handler_rtos_ret_code_t(*)(void*, void*, uint32_t))os_queue_receive,
    };

    /* 3. 临界区接口定义 */
    static led_handler_os_critical_t led_handler_os_critical;
    led_handler_os_critical.pf_rtos_critical_enter = (led_handler_rtos_ret_code_t(*)(void))os_critical_enter;
    led_handler_os_critical.pf_rtos_critical_exit  = (led_handler_rtos_ret_code_t(*)(void))os_critical_exit;

    /* 4. 动态分配接口定义 */
    static led_handler_dynamic_allocation_t led_handler_dynamic_allocation;
    led_handler_dynamic_allocation.pfmalloc = pvPortMalloc;
    led_handler_dynamic_allocation.pffree   = vPortFree;

    /* 5. 时基接口定义 */
    static led_handler_timebase_interface_t led_handler_timebase_interface;
    led_handler_timebase_interface.pfget_timetick_ms = HAL_GetTick;

    /* 6. 实例化LED_RUN驱动 */
    static bsp_led_driver_t led_run_driver;
    static led_gpio_interface_t led_run_gpio_interface;
    if (SYS_ADAPTION_OK != led_driver_inst(&led_run_driver, &led_run_gpio_interface,
                                          LED_RUN, LED_RUN_GPIO_Port, LED_RUN_Pin)) {
        SYSTEM_ADAPTION_LOG_ERROR("LED_RUN driver instance failed");
        return SYS_ADAPTION_ERROR;
    }
    static bsp_led_driver_t led_485_driver;
    static led_gpio_interface_t led_485_gpio_interface;
    if (SYS_ADAPTION_OK != led_driver_inst(&led_485_driver, &led_485_gpio_interface,
                                          LED_485, LED_485_GPIO_Port, LED_485_Pin)) 
    {
        SYSTEM_ADAPTION_LOG_ERROR("LED_485 driver instance failed");
        return SYS_ADAPTION_ERROR;
    }
    /* 7. 构造LED处理器输入参数 */
    static led_handler_input_all_arg_t led_handler_input;
    led_handler_input.p_system_config         = &led_handler_system_config;
    led_handler_input.p_led_os_interface      = &led_handler_os_interface;
    led_handler_input.p_led_os_critical       = &led_handler_os_critical;
    led_handler_input.p_led_dynamic_allocation = &led_handler_dynamic_allocation;
    led_handler_input.p_led_timebase_interface = &led_handler_timebase_interface;

    /* 8. LED处理器初始化 */
    if(LED_HANDLER_OK != bsp_led_handler_instance(&g_led_handler, &led_handler_input))
    {
        SYSTEM_ADAPTION_LOG_ERROR("bsp_led_handler_instance failed");
        return SYS_ADAPTION_ERROR;
    }

    /* 9. 注册LED_RUN驱动到LED处理器 */
    if(LED_HANDLER_OK != bsp_led_handler_register_driver(&g_led_handler, &led_run_driver))
    {
        SYSTEM_ADAPTION_LOG_ERROR("bsp_led_handler_register_driver failed for LED_RUN");
        return SYS_ADAPTION_ERROR;
    }
    led_control_param_t led_run_param = 
    {
        .led_which = LED_RUN,
        .mode = LED_MODE_BLINK,
        .cycle_time_ms = 1000,           /* 1秒周期 */
        .blink_count = 0,                /* 无限次 */
        .proportion = LED_BLINK_RATIO_1_1 /* 50%占空比 */
    };
    if(LED_HANDLER_OK != bsp_led_handler_control(&g_led_handler, &led_run_param, 0))
    {
        // 处理LED控制失败的情况
        SYSTEM_ADAPTION_LOG_ERROR("bsp_led_handler_control failed for LED_RUN");
        return SYS_ADAPTION_ERROR;
    }
    /* 10. 注册LED_485驱动到LED处理器 */
    if(LED_HANDLER_OK != bsp_led_handler_register_driver(&g_led_handler, &led_485_driver))
    {
        SYSTEM_ADAPTION_LOG_ERROR("bsp_led_handler_register_driver failed for LED_485");
        return SYS_ADAPTION_ERROR;
    }
    /* 11. 初始化LED_485串口指示器 */
    uart_led_indicator_config_t led_485_config =
    {
        .p_led_handler = &g_led_handler,
        .led_which = LED_485,
        .timeout_ms = 100,
        .work_mode = UART_LED_WORK_MODE_EVENT_DRIVEN,
    };
    led_485_config.p_timebase_interface = (uart_led_indicator_timebase_interface_t*)&led_handler_timebase_interface;
    if(UART_LED_INDICATOR_OK != uart_led_indicator_init(&led_485_config))
    {
        SYSTEM_ADAPTION_LOG_ERROR("uart_led_indicator_init failed");
        return SYS_ADAPTION_ERROR;
    }
    SYSTEM_ADAPTION_LOG_DEUBG("led_handler_inst end");
    return SYS_ADAPTION_OK;
}

/******************************************************************************
 * @brief 获取LED处理器实例
 *
 * @return bsp_led_handler_t* LED处理器实例指针
 *****************************************************************************/
bsp_led_handler_t* get_led_handler_instance(void)
{
    return &g_led_handler;
}

//*************************** led_handler api ********************************//


//*************************** GPIO EXTI CALLBACK api ************************//
GPIO_EXTI_CALLBACK_FUN
{
    if(AD7606_BUSY_Pin == GPIO_Pin)
    {
        g_adc_busy_irq_callbackfun(g_adc_busy_irq_callbackfun_arg);
    }
}

//*************************** GPIO EXTI CALLBACK api ************************//

//*************************** TIM IC CALLBACK api ************************//
#include "timestamp_manager_v2.h"

// TIM3频率测量变量 (1MHz时钟，连续运行模式) - 保留用于兼容性
uint32_t tim3_ic_val1 = 0, tim3_ic_val2 = 0, tim3_diff = 0;
uint8_t tim3_is_first_captured = 0;
float tim3_freq = 0;
uint32_t tim3_pulse_width = 0;  // 脉宽测量

// TIM4频率测量变量 (1MHz时钟，连续运行模式) - 保留用于兼容性
uint32_t tim4_ic_val1 = 0, tim4_ic_val2 = 0, tim4_diff = 0;
uint8_t tim4_is_first_captured = 0;
float tim4_freq = 0;

#define TIM3_CLOCK_FREQ     1000000.0f   // TIM3时钟频率1MHz (100MHz/100)
#define TIM4_CLOCK_FREQ     1000000.0f      // TIM4时钟频率1MHz (100MHz/100)

TIM_IC_CALLBACK_FUN
{
    /* 新的时间戳同步处理 */
    if(htim->Instance == TIM3)
    {
        /* 调用时间戳管理器的TIM3回调 */
        timestamp_manager_v2_tim3_ic_callback(htim);

        /* 保留原有的频率测量逻辑用于兼容性 */
        if(htim->Channel == HAL_TIM_ACTIVE_CHANNEL_1)  // 上升沿捕获
        {
            if(tim3_is_first_captured == 0)
            {
                tim3_ic_val1 = HAL_TIM_ReadCapturedValue(htim, TIM_CHANNEL_1);
                tim3_is_first_captured = 1;
            }
            else
            {
                tim3_ic_val2 = HAL_TIM_ReadCapturedValue(htim, TIM_CHANNEL_1);

                // 计算两个上升沿之间的时间差（周期）
                if(tim3_ic_val2 > tim3_ic_val1)
                {
                    tim3_diff = tim3_ic_val2 - tim3_ic_val1;
                }
                else
                {
                    // 处理计数器溢出情况（从65535回绕到0）
                    tim3_diff = (65536 - tim3_ic_val1) + tim3_ic_val2;
                }

                // 频率计算：f = 时钟频率 / 计数差值
                tim3_freq = TIM3_CLOCK_FREQ / tim3_diff;
                tim3_is_first_captured = 0;
            }
        }
        else if(htim->Channel == HAL_TIM_ACTIVE_CHANNEL_2)  // 下降沿捕获（间接模式）
        {
            // 测量脉宽：从上升沿到下降沿的时间
            if(tim3_is_first_captured == 1)
            {
                uint32_t falling_edge = HAL_TIM_ReadCapturedValue(htim, TIM_CHANNEL_2);
                // 连续运行模式下的脉宽计算
                if(falling_edge > tim3_ic_val1)
                    tim3_pulse_width = falling_edge - tim3_ic_val1;
                else
                    tim3_pulse_width = (65536 - tim3_ic_val1) + falling_edge;

                // 脉宽时间 = tim3_pulse_width / TIM3_CLOCK_FREQ (秒)
                // 占空比 = tim3_pulse_width / tim3_diff * 100%
                if(tim3_diff > 0)
                {
                    float duty_cycle = ((float)tim3_pulse_width / (float)tim3_diff) * 100.0f;
                    SYSTEM_ADAPTION_LOG_INFO("TIM3: pulse width=%lu, duty cycle=%.2f%%\r\n", tim3_pulse_width, duty_cycle);
                }
            }
        }
    }
    else if(htim->Instance == TIM4)
    {
        /* 调用时间戳管理器的TIM4回调 */
        timestamp_manager_v2_tim4_ic_callback(htim);

        /* 保留原有的频率测量逻辑用于兼容性 */
        if(htim->Channel == HAL_TIM_ACTIVE_CHANNEL_1)  // TIM4 CH1处理
        {
            if(tim4_is_first_captured == 0)
            {
                tim4_ic_val1 = HAL_TIM_ReadCapturedValue(htim, TIM_CHANNEL_1);
                tim4_is_first_captured = 1;
            }
            else
            {
                tim4_ic_val2 = HAL_TIM_ReadCapturedValue(htim, TIM_CHANNEL_1);

                // 计算两个上升沿之间的时间差（周期）
                if(tim4_ic_val2 > tim4_ic_val1)
                {
                    tim4_diff = tim4_ic_val2 - tim4_ic_val1;
                }
                else
                {
                    // 处理计数器溢出情况
                    tim4_diff = (65536 - tim4_ic_val1) + tim4_ic_val2;
                }

                // TIM4使用1MHz时钟
                tim4_freq = TIM4_CLOCK_FREQ / tim4_diff;
                tim4_is_first_captured = 0;
            }
        }
        else if(htim->Channel == HAL_TIM_ACTIVE_CHANNEL_2)  // TIM4 CH2处理
        {
            // TIM4 CH2的处理逻辑
        }
    }
}


//*************************** system visual config api **********************//
#ifdef SYSTEM_VISUAL_OUTPUT_ENABLE
void system_visual_output_msg(void *arg)
{
    SYSTEM_ADAPTION_LOG_DEUBG("system_visual_output_msg start");
    static char InfoBuffer[512] = {0};
    BaseType_t val;
    while(1)
    {
        vTaskList((char *) &InfoBuffer);
        printf("-----------------------------------\n");
		printf("name          state  priority  stack   num\n");
        printf("\r\n%s\r\n", InfoBuffer);
    
        /* 查询系统历史剩余最小内存 */
        printf("Total FreeRTOS memory size: %d bytes.\r\n", 
                        configTOTAL_HEAP_SIZE);    // 打印FreeRTOS总内存大小（单位字节）
                        
        printf("The current remaining heap memory size is: %d bytes.\r\n", 
                        xPortGetFreeHeapSize());   // 查询当前剩余堆内存大小
                        
        printf("Historical minimum remaining memory size: %d bytes.\r\n\r\n", 
                        xPortGetMinimumEverFreeHeapSize());     // 查询历史剩余最小内存大小

        vTaskDelay(5000);
    }
}

sys_adaption_ret_t system_visual_config_inst(void)
{
    SYSTEM_ADAPTION_LOG_DEUBG("system_visual_config_inst start");
    /* 系统可视化配置 */
    if(pdTRUE != xTaskCreate(system_visual_output_msg, 
                          "system_visual_output_msg", 
                          g_sys_adaption_task_stack.\
                          system_visual_output_msg_stack_size, 
                          NULL, 
                          g_sys_adaption_task_priority.\
                          system_visual_output_msg_priority, 
                          NULL))
    {
        SYSTEM_ADAPTION_LOG_ERROR("system_visual_output_msg create failed");
        return SYS_ADAPTION_ERROR;
    }
    SYSTEM_ADAPTION_LOG_DEUBG("system_visual_config_inst end");
    return SYS_ADAPTION_OK;
}
#endif /* SYSTEM_VISUAL_OUTPUT_ENABLE */
//*************************** system visual config api **********************//

//*************************** system source api *****************************//


sys_adaption_ret_t system_source_inst(void)
{
    SYSTEM_ADAPTION_LOG_DEUBG("system_source_inst start");
    /* 采集接口实例化 */
   if( SYS_ADAPTION_OK != adc_collect_handler_source_inst())
   {
       SYSTEM_ADAPTION_LOG_ERROR("adc_collect_handler_source_inst failed");
       return SYS_ADAPTION_ERROR;
   } 
   /* 事件处理句柄实例化 */
   if (SYS_ADAPTION_OK != data_process_task_instance())
   {
       SYSTEM_ADAPTION_LOG_ERROR("data_process_task_instance failed");
       return SYS_ADAPTION_ERROR;
   }
   /* fft计算任务实例化 */
   if (SYS_ADAPTION_OK != fft_calculate_task_instance())
   {
       SYSTEM_ADAPTION_LOG_ERROR("fft_calculate_task_instance failed");
       return SYS_ADAPTION_ERROR;
   }
   /* flash事件处理句柄实例化 */
   if( SYS_ADAPTION_OK != flash_event_handler_inst())
   {
       SYSTEM_ADAPTION_LOG_ERROR("flash_event_handler_inst failed");
       return SYS_ADAPTION_ERROR;
   }
   /* rs485事件处理句柄实例化 */
   if( SYS_ADAPTION_OK != rs485_event_handler_inst())
   {
       SYSTEM_ADAPTION_LOG_ERROR("rs485_event_handler_inst failed");
       return SYS_ADAPTION_ERROR;
   }
    /* LED处理器实例化 */
    if( SYS_ADAPTION_OK != led_handler_inst())
    {
        SYSTEM_ADAPTION_LOG_ERROR("led_handler_inst failed");
        return SYS_ADAPTION_ERROR;
    }
#ifdef SYSTEM_VISUAL_OUTPUT_ENABLE
    /* 系统可视化配置 */
    if( SYS_ADAPTION_OK != system_visual_config_inst())
    {
        SYSTEM_ADAPTION_LOG_ERROR("system_visual_config_inst failed");
        return SYS_ADAPTION_ERROR;
    }
#endif /* SYSTEM_VISUAL_OUTPUT_ENABLE */
    /* 零点检测任务初始化 */
    if( SYS_ADAPTION_OK != zero_detect_task_inst())
    {
        SYSTEM_ADAPTION_LOG_ERROR("zero_detect_task_inst failed");
        return SYS_ADAPTION_ERROR;
    }

    SYSTEM_ADAPTION_LOG_DEUBG("system_source_inst end");
    return SYS_ADAPTION_OK;
}

/******************************************************************************
 * @brief 零点检测任务实例化
 *
 * @return sys_adaption_ret_t
 *****************************************************************************/
sys_adaption_ret_t zero_detect_task_inst(void)
{
    SYSTEM_ADAPTION_LOG_DEUBG("zero_detect_task_inst start");

    /* 配置零点检测任务参数 */
    zero_detect_config_t zero_detect_config = zero_detect_get_default_config();
    zero_detect_config.task_stack_size = g_sys_adaption_task_stack.zero_detect_task_stack_size;
    zero_detect_config.task_priority = g_sys_adaption_task_priority.zero_detect_task_priority;

    /* 配置时间戳管理器参数 */
    timestamp_manager_config_t timestamp_config = timestamp_manager_v2_get_default_config();

    /* 初始化集成系统 */
    zero_detect_ret_t ret = zero_detect_integration_init(&zero_detect_config, &timestamp_config);
    if (ret != ZERO_DETECT_OK) {
        SYSTEM_ADAPTION_LOG_ERROR("zero_detect_integration_init failed: %d", ret);
        return SYS_ADAPTION_ERROR;
    }

    /* 启动集成系统 */
    ret = zero_detect_integration_start();
    if (ret != ZERO_DETECT_OK) {
        SYSTEM_ADAPTION_LOG_ERROR("zero_detect_integration_start failed: %d", ret);
        return SYS_ADAPTION_ERROR;
    }

    SYSTEM_ADAPTION_LOG_DEUBG("zero_detect_task_inst end");
    return SYS_ADAPTION_OK;
}