#include "zero_detect_task.h"
#include "timestamp_manager_v2.h"
#include <stdio.h>

/* 集成模块 - 将ZeroDetectTask与timestamp_manager_v2连接 */

/* 调试日志宏 */
#if TIMESTAMP_DEBUG_ENABLE
#define INTEGRATION_LOG_DEBUG(fmt, ...) printf("[ZeroDetectIntegration] " fmt "\r\n", ##__VA_ARGS__)
#define INTEGRATION_LOG_INFO(fmt, ...)  printf("[ZeroDetectIntegration] " fmt "\r\n", ##__VA_ARGS__)
#define INTEGRATION_LOG_ERROR(fmt, ...) printf("[ZeroDetectIntegration] ERROR: " fmt "\r\n", ##__VA_ARGS__)
#else
#define INTEGRATION_LOG_DEBUG(fmt, ...)
#define INTEGRATION_LOG_INFO(fmt, ...)
#define INTEGRATION_LOG_ERROR(fmt, ...)
#endif

/* 集成状态 */
static bool g_integration_initialized = false;

/**
 * @brief 初始化零点检测任务与时间戳管理器的集成
 * @param zero_detect_config 零点检测任务配置，NULL使用默认配置
 * @param timestamp_config 时间戳管理器配置，NULL使用默认配置
 * @return zero_detect_ret_t 返回值
 */
zero_detect_ret_t zero_detect_integration_init(const zero_detect_config_t *zero_detect_config,
                                              const timestamp_manager_config_t *timestamp_config)
{
    if (g_integration_initialized) {
        INTEGRATION_LOG_ERROR("Integration already initialized");
        return ZERO_DETECT_ERROR;
    }

    INTEGRATION_LOG_INFO("Initializing integration...");

    /* 1. 初始化时间戳管理器 */
    timestamp_manager_ret_t ts_ret = timestamp_manager_v2_init(timestamp_config);
    if (ts_ret != TIMESTAMP_MANAGER_OK) {
        INTEGRATION_LOG_ERROR("Failed to initialize timestamp manager: %d", ts_ret);
        return ZERO_DETECT_ERROR;
    }

    /* 2. 初始化零点检测任务 */
    zero_detect_ret_t zd_ret = zero_detect_global_init(zero_detect_config);
    if (zd_ret != ZERO_DETECT_OK) {
        INTEGRATION_LOG_ERROR("Failed to initialize zero detect task: %d", zd_ret);
        timestamp_manager_v2_deinit();
        return zd_ret;
    }

    /* 3. 注册回调函数到时间戳管理器 */
    timestamp_manager_callbacks_t callbacks = {
        .on_zero_cross = zero_detect_task_tim4_callback,
        .on_signal_freq_update = zero_detect_task_tim3_callback,
        .on_overflow = zero_detect_task_overflow_callback,
        .on_error = (timestamp_manager_error_callback_t)zero_detect_task_error_callback,
        .user_data = NULL
    };

    ts_ret = timestamp_manager_v2_register_callbacks(&callbacks);
    if (ts_ret != TIMESTAMP_MANAGER_OK) {
        INTEGRATION_LOG_ERROR("Failed to register callbacks: %d", ts_ret);
        zero_detect_global_deinit();
        timestamp_manager_v2_deinit();
        return ZERO_DETECT_ERROR;
    }

    g_integration_initialized = true;
    INTEGRATION_LOG_INFO("Integration initialized successfully");
    return ZERO_DETECT_OK;
}

/**
 * @brief 启动集成系统
 * @return zero_detect_ret_t 返回值
 */
zero_detect_ret_t zero_detect_integration_start(void)
{
    if (!g_integration_initialized) {
        INTEGRATION_LOG_ERROR("Integration not initialized");
        return ZERO_DETECT_ERROR_NOT_INIT;
    }

    INTEGRATION_LOG_INFO("Starting integration...");

    /* 1. 启动时间戳管理器 */
    timestamp_manager_ret_t ts_ret = timestamp_manager_v2_start();
    if (ts_ret != TIMESTAMP_MANAGER_OK) {
        INTEGRATION_LOG_ERROR("Failed to start timestamp manager: %d", ts_ret);
        return ZERO_DETECT_ERROR;
    }

    /* 2. 启动零点检测任务 */
    zero_detect_ret_t zd_ret = zero_detect_global_start();
    if (zd_ret != ZERO_DETECT_OK) {
        INTEGRATION_LOG_ERROR("Failed to start zero detect task: %d", zd_ret);
        timestamp_manager_v2_stop();
        return zd_ret;
    }

    INTEGRATION_LOG_INFO("Integration started successfully");
    return ZERO_DETECT_OK;
}

/**
 * @brief 停止集成系统
 * @return zero_detect_ret_t 返回值
 */
zero_detect_ret_t zero_detect_integration_stop(void)
{
    if (!g_integration_initialized) {
        return ZERO_DETECT_ERROR_NOT_INIT;
    }

    INTEGRATION_LOG_INFO("Stopping integration...");

    /* 1. 停止零点检测任务 */
    zero_detect_ret_t zd_ret = zero_detect_global_stop();
    if (zd_ret != ZERO_DETECT_OK) {
        INTEGRATION_LOG_ERROR("Failed to stop zero detect task: %d", zd_ret);
    }

    /* 2. 停止时间戳管理器 */
    timestamp_manager_ret_t ts_ret = timestamp_manager_v2_stop();
    if (ts_ret != TIMESTAMP_MANAGER_OK) {
        INTEGRATION_LOG_ERROR("Failed to stop timestamp manager: %d", ts_ret);
    }

    INTEGRATION_LOG_INFO("Integration stopped");
    return ZERO_DETECT_OK;
}

/**
 * @brief 反初始化集成系统
 * @return zero_detect_ret_t 返回值
 */
zero_detect_ret_t zero_detect_integration_deinit(void)
{
    if (!g_integration_initialized) {
        return ZERO_DETECT_ERROR_NOT_INIT;
    }

    INTEGRATION_LOG_INFO("Deinitializing integration...");

    /* 1. 停止系统 */
    zero_detect_integration_stop();

    /* 2. 反初始化零点检测任务 */
    zero_detect_ret_t zd_ret = zero_detect_global_deinit();
    if (zd_ret != ZERO_DETECT_OK) {
        INTEGRATION_LOG_ERROR("Failed to deinit zero detect task: %d", zd_ret);
    }

    /* 3. 反初始化时间戳管理器 */
    timestamp_manager_ret_t ts_ret = timestamp_manager_v2_deinit();
    if (ts_ret != TIMESTAMP_MANAGER_OK) {
        INTEGRATION_LOG_ERROR("Failed to deinit timestamp manager: %d", ts_ret);
    }

    g_integration_initialized = false;
    INTEGRATION_LOG_INFO("Integration deinitialized");
    return ZERO_DETECT_OK;
}

/**
 * @brief 获取集成状态
 * @param zero_detect_status 零点检测状态输出
 * @param timestamp_status 时间戳管理器状态输出
 * @return zero_detect_ret_t 返回值
 */
zero_detect_ret_t zero_detect_integration_get_status(zero_detect_status_t *zero_detect_status,
                                                    timestamp_manager_status_t *timestamp_status)
{
    if (!g_integration_initialized) {
        return ZERO_DETECT_ERROR_NOT_INIT;
    }

    /* 获取零点检测状态 */
    if (zero_detect_status != NULL) {
        zero_detect_ret_t ret = zero_detect_get_global_status(zero_detect_status);
        if (ret != ZERO_DETECT_OK) {
            INTEGRATION_LOG_ERROR("Failed to get zero detect status: %d", ret);
            return ret;
        }
    }

    /* 获取时间戳管理器状态 */
    if (timestamp_status != NULL) {
        timestamp_manager_ret_t ret = timestamp_manager_v2_get_manager_status(timestamp_status);
        if (ret != TIMESTAMP_MANAGER_OK) {
            INTEGRATION_LOG_ERROR("Failed to get timestamp manager status: %d", ret);
            return ZERO_DETECT_ERROR;
        }
    }

    return ZERO_DETECT_OK;
}

/**
 * @brief 生成压缩时间戳 - 供AD7606驱动调用
 * @return uint32_t 压缩时间戳，0表示失败
 */
uint32_t zero_detect_integration_generate_packed_timestamp(void)
{
    if (!g_integration_initialized) {
        return 0;
    }

    return timestamp_manager_v2_generate_packed_timestamp();
}

/**
 * @brief 计算相位修正参数
 * @param packed_timestamp 压缩时间戳
 * @param correction 输出相位修正参数
 * @return zero_detect_ret_t 返回值
 */
zero_detect_ret_t zero_detect_integration_calculate_phase_correction(uint32_t packed_timestamp,
                                                                   timestamp_phase_correction_t *correction)
{
    if (!g_integration_initialized) {
        return ZERO_DETECT_ERROR_NOT_INIT;
    }

    if (correction == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    timestamp_manager_ret_t ret = timestamp_manager_v2_calculate_phase_correction(packed_timestamp, correction);
    if (ret != TIMESTAMP_MANAGER_OK) {
        INTEGRATION_LOG_ERROR("Failed to calculate phase correction: %d", ret);
        return ZERO_DETECT_ERROR;
    }

    return ZERO_DETECT_OK;
}

/**
 * @brief 应用相位修正到FFT结果
 * @param fft_complex_data FFT复数数据
 * @param fft_length FFT长度
 * @param correction 相位修正参数
 * @return zero_detect_ret_t 返回值
 */
zero_detect_ret_t zero_detect_integration_apply_phase_correction(float *fft_complex_data,
                                                               uint16_t fft_length,
                                                               const timestamp_phase_correction_t *correction)
{
    if (!g_integration_initialized) {
        return ZERO_DETECT_ERROR_NOT_INIT;
    }

    if (fft_complex_data == NULL || correction == NULL) {
        return ZERO_DETECT_ERROR_PARAM;
    }

    timestamp_manager_ret_t ret = timestamp_manager_v2_apply_phase_correction(fft_complex_data, 
                                                                            fft_length, 
                                                                            correction);
    if (ret != TIMESTAMP_MANAGER_OK) {
        INTEGRATION_LOG_ERROR("Failed to apply phase correction: %d", ret);
        return ZERO_DETECT_ERROR;
    }

    return ZERO_DETECT_OK;
}

/**
 * @brief 打印集成状态
 */
void zero_detect_integration_print_status(void)
{
    printf("=== Zero Detect Integration Status ===\r\n");
    printf("Initialized: %s\r\n", g_integration_initialized ? "Yes" : "No");
    
    if (g_integration_initialized) {
        zero_detect_status_t zd_status;
        timestamp_manager_status_t ts_status;
        
        if (zero_detect_integration_get_status(&zd_status, &ts_status) == ZERO_DETECT_OK) {
            printf("--- Zero Detect Status ---\r\n");
            printf("Grid Frequency: %.2f Hz (Valid: %s)\r\n", 
                   zd_status.grid_freq_hz, zd_status.freq_valid ? "Yes" : "No");
            printf("Signal Frequency: %.2f Hz\r\n", zd_status.signal_freq_hz);
            printf("Capture Count: %u\r\n", zd_status.capture_count);
            printf("T Zero: %u\r\n", zd_status.t_zero);
            printf("Total Zero Cross: %u\r\n", (unsigned int)zd_status.total_zero_cross_count);
            
            printf("--- Timestamp Manager Status ---\r\n");
            printf("Running: %s\r\n", ts_status.running ? "Yes" : "No");
            printf("Error Count: %u\r\n", (unsigned int)ts_status.error_count);
            printf("Overflow Count: %u\r\n", (unsigned int)ts_status.overflow_count);
        }
    }
    
    printf("=====================================\r\n");
}
