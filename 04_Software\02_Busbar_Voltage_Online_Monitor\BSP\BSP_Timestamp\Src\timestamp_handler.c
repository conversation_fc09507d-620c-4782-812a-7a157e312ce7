#include "timestamp_handler.h"
#include <string.h>
#include <stdio.h>

/* 内部函数声明 */
static void timestamp_handler_driver_callback(timestamp_driver_tim_type_t tim_type, 
                                             const timestamp_driver_capture_data_t *capture_data,
                                             void *user_data);
static timestamp_handler_ret_t timestamp_handler_process_grid_capture(timestamp_handler_handle_t *handle,
                                                                     const timestamp_driver_capture_data_t *capture_data);
static timestamp_handler_ret_t timestamp_handler_process_signal_capture(timestamp_handler_handle_t *handle,
                                                                       const timestamp_driver_capture_data_t *capture_data);

/**
 * @brief 初始化时间戳处理器
 */
timestamp_handler_ret_t timestamp_handler_init(timestamp_handler_handle_t *handle,
                                              const timestamp_config_t *config)
{
    if (handle == NULL) {
        return TIMESTAMP_HANDLER_ERROR_PARAM;
    }

    /* 清零句柄 */
    memset(handle, 0, sizeof(timestamp_handler_handle_t));

    /* 设置配置 */
    if (config != NULL) {
        handle->config = *config;
    } else {
        handle->config = timestamp_get_default_config();
    }

    /* 初始化电网同步状态 */
    handle->sync_status.capture_count = 0;
    handle->sync_status.t_zero = 0;
    handle->sync_status.grid_freq_hz = handle->config.nominal_freq_hz;
    handle->sync_status.signal_freq_hz = handle->config.nominal_freq_hz;

    /* 初始化统计信息 */
    timestamp_utils_init_statistics(&handle->statistics);

    /* 初始化频率测量结果 */
    memset(&handle->grid_freq_result, 0, sizeof(handle->grid_freq_result));
    memset(&handle->signal_freq_result, 0, sizeof(handle->signal_freq_result));
    handle->grid_freq_result.frequency_hz = handle->config.nominal_freq_hz;
    handle->signal_freq_result.frequency_hz = handle->config.nominal_freq_hz;

    /* 初始化驱动层 */
    timestamp_driver_ret_t driver_ret = timestamp_driver_init(&handle->driver, &handle->config);
    if (driver_ret != TIMESTAMP_DRIVER_OK) {
        return TIMESTAMP_HANDLER_ERROR;
    }

    /* 注册驱动回调 */
    timestamp_driver_register_callback(&handle->driver, timestamp_handler_driver_callback, handle);

    /* 设置默认频率测量策略 */
    if (handle->config.freq_strategy == TIMESTAMP_FREQ_STRATEGY_FILTERED) {
        handle->grid_freq_strategy = timestamp_handler_get_filtered_freq_strategy();
        handle->signal_freq_strategy = timestamp_handler_get_filtered_freq_strategy();
    } else if (handle->config.freq_strategy == TIMESTAMP_FREQ_STRATEGY_ADAPTIVE) {
        handle->grid_freq_strategy = timestamp_handler_get_adaptive_freq_strategy();
        handle->signal_freq_strategy = timestamp_handler_get_adaptive_freq_strategy();
    } else {
        handle->grid_freq_strategy = timestamp_handler_get_simple_freq_strategy();
        handle->signal_freq_strategy = timestamp_handler_get_simple_freq_strategy();
    }

    /* 初始化策略 */
    if (handle->grid_freq_strategy && handle->grid_freq_strategy->init) {
        handle->grid_freq_strategy->init(handle->grid_strategy_data, &handle->config);
    }
    if (handle->signal_freq_strategy && handle->signal_freq_strategy->init) {
        handle->signal_freq_strategy->init(handle->signal_strategy_data, &handle->config);
    }

    handle->initialized = true;
    return TIMESTAMP_HANDLER_OK;
}

/**
 * @brief 反初始化时间戳处理器
 */
timestamp_handler_ret_t timestamp_handler_deinit(timestamp_handler_handle_t *handle)
{
    if (handle == NULL || !handle->initialized) {
        return TIMESTAMP_HANDLER_ERROR_PARAM;
    }

    /* 停止处理器 */
    timestamp_handler_stop(handle);

    /* 反初始化驱动层 */
    timestamp_driver_deinit(&handle->driver);

    handle->initialized = false;
    return TIMESTAMP_HANDLER_OK;
}

/**
 * @brief 启动时间戳处理器
 */
timestamp_handler_ret_t timestamp_handler_start(timestamp_handler_handle_t *handle)
{
    if (handle == NULL || !handle->initialized) {
        return TIMESTAMP_HANDLER_ERROR_NOT_INIT;
    }

    /* 启动电网零点捕获 */
    timestamp_driver_ret_t ret = timestamp_driver_start_capture(&handle->driver, TIMESTAMP_DRIVER_TIM_GRID_ZERO);
    if (ret != TIMESTAMP_DRIVER_OK) {
        return TIMESTAMP_HANDLER_ERROR;
    }

    /* 启动信号频率检测 */
    ret = timestamp_driver_start_capture(&handle->driver, TIMESTAMP_DRIVER_TIM_SIGNAL_FREQ);
    if (ret != TIMESTAMP_DRIVER_OK) {
        timestamp_driver_stop_capture(&handle->driver, TIMESTAMP_DRIVER_TIM_GRID_ZERO);
        return TIMESTAMP_HANDLER_ERROR;
    }

    return TIMESTAMP_HANDLER_OK;
}

/**
 * @brief 停止时间戳处理器
 */
timestamp_handler_ret_t timestamp_handler_stop(timestamp_handler_handle_t *handle)
{
    if (handle == NULL || !handle->initialized) {
        return TIMESTAMP_HANDLER_ERROR_NOT_INIT;
    }

    /* 停止所有捕获 */
    timestamp_driver_stop_capture(&handle->driver, TIMESTAMP_DRIVER_TIM_GRID_ZERO);
    timestamp_driver_stop_capture(&handle->driver, TIMESTAMP_DRIVER_TIM_SIGNAL_FREQ);

    return TIMESTAMP_HANDLER_OK;
}

/**
 * @brief 获取电网同步状态
 */
timestamp_handler_ret_t timestamp_handler_get_sync_status(timestamp_handler_handle_t *handle,
                                                         timestamp_grid_sync_status_t *status)
{
    if (handle == NULL || status == NULL || !handle->initialized) {
        return TIMESTAMP_HANDLER_ERROR_PARAM;
    }

    *status = handle->sync_status;
    return TIMESTAMP_HANDLER_OK;
}

/**
 * @brief 生成压缩时间戳
 */
uint32_t timestamp_handler_generate_packed_timestamp(timestamp_handler_handle_t *handle,
                                                    uint16_t t_sample0)
{
    if (handle == NULL || !handle->initialized) {
        return 0;
    }

    return timestamp_utils_pack(handle->sync_status.capture_count, t_sample0);
}

/**
 * @brief 计算相位修正参数
 */
timestamp_handler_ret_t timestamp_handler_calculate_phase_correction(timestamp_handler_handle_t *handle,
                                                                    uint32_t packed_timestamp,
                                                                    timestamp_phase_correction_t *correction)
{
    if (handle == NULL || correction == NULL || !handle->initialized) {
        return TIMESTAMP_HANDLER_ERROR_PARAM;
    }

    if (!handle->grid_freq_result.valid) {
        return TIMESTAMP_HANDLER_ERROR;
    }

    /* 解包时间戳 */
    uint16_t t_sample0 = timestamp_utils_unpack_t0(packed_timestamp);
    uint16_t t_zero = handle->sync_status.t_zero;

    /* 使用工具函数计算相位修正 */
    timestamp_utils_ret_t ret = timestamp_utils_calc_phase_correction(
        t_sample0, t_zero, handle->grid_freq_result.frequency_hz,
        handle->config.timer_freq_hz, correction);

    return (ret == TIMESTAMP_UTILS_OK) ? TIMESTAMP_HANDLER_OK : TIMESTAMP_HANDLER_ERROR;
}

/**
 * @brief 检查并处理溢出
 */
timestamp_handler_ret_t timestamp_handler_check_overflow(timestamp_handler_handle_t *handle)
{
    if (handle == NULL || !handle->initialized) {
        return TIMESTAMP_HANDLER_ERROR_PARAM;
    }

    if (handle->sync_status.capture_count >= handle->config.capture_overflow_threshold) {
        /* 触发溢出事件 */
        if (handle->observer.on_overflow) {
            handle->observer.on_overflow(handle->sync_status.capture_count, handle->observer_user_data);
        }

        /* 清零capture_count */
        uint16_t old_count = handle->sync_status.capture_count;
        handle->sync_status.capture_count = 0;
        handle->overflow_reset_pending = true;

        /* 更新统计 */
        handle->statistics.overflow_count++;

        return TIMESTAMP_HANDLER_ERROR_OVERFLOW;
    }

    return TIMESTAMP_HANDLER_OK;
}

/**
 * @brief 驱动回调函数
 */
static void timestamp_handler_driver_callback(timestamp_driver_tim_type_t tim_type, 
                                             const timestamp_driver_capture_data_t *capture_data,
                                             void *user_data)
{
    timestamp_handler_handle_t *handle = (timestamp_handler_handle_t *)user_data;
    if (handle == NULL || !handle->initialized || capture_data == NULL) {
        return;
    }

    if (tim_type == TIMESTAMP_DRIVER_TIM_GRID_ZERO) {
        timestamp_handler_process_grid_capture(handle, capture_data);
    } else if (tim_type == TIMESTAMP_DRIVER_TIM_SIGNAL_FREQ) {
        timestamp_handler_process_signal_capture(handle, capture_data);
    }
}

/**
 * @brief 处理电网零点捕获
 */
static timestamp_handler_ret_t timestamp_handler_process_grid_capture(timestamp_handler_handle_t *handle,
                                                                     const timestamp_driver_capture_data_t *capture_data)
{
    /* 计算周期 */
    uint16_t period_ticks = timestamp_utils_calc_timer_diff(capture_data->capture_value, handle->last_grid_t_zero);
    
    /* 使用策略处理频率测量 */
    if (handle->grid_freq_strategy && handle->grid_freq_strategy->process) {
        handle->grid_freq_strategy->process(handle->grid_strategy_data, period_ticks, &handle->grid_freq_result);
    }

    /* 更新同步状态 */
    handle->last_grid_t_zero = handle->sync_status.t_zero;
    handle->sync_status.t_zero = capture_data->capture_value;
    handle->sync_status.capture_count++;
    handle->sync_status.grid_freq_hz = handle->grid_freq_result.frequency_hz;

    /* 更新统计 */
    timestamp_utils_update_statistics(&handle->statistics, handle->grid_freq_result.frequency_hz, 
                                    handle->grid_freq_result.valid);

    /* 触发零点捕获事件 */
    if (handle->observer.on_zero_cross) {
        handle->observer.on_zero_cross(handle->sync_status.capture_count, handle->sync_status.t_zero,
                                     handle->grid_freq_result.frequency_hz, handle->observer_user_data);
    }

    /* 检查溢出 */
    timestamp_handler_check_overflow(handle);

    return TIMESTAMP_HANDLER_OK;
}

/**
 * @brief 处理信号频率捕获
 */
static timestamp_handler_ret_t timestamp_handler_process_signal_capture(timestamp_handler_handle_t *handle,
                                                                       const timestamp_driver_capture_data_t *capture_data)
{
    /* 计算周期 */
    uint16_t period_ticks = timestamp_utils_calc_timer_diff(capture_data->capture_value, handle->last_signal_capture);
    
    /* 使用策略处理频率测量 */
    if (handle->signal_freq_strategy && handle->signal_freq_strategy->process) {
        handle->signal_freq_strategy->process(handle->signal_strategy_data, period_ticks, &handle->signal_freq_result);
    }

    /* 更新同步状态 */
    handle->last_signal_capture = capture_data->capture_value;
    handle->sync_status.signal_freq_hz = handle->signal_freq_result.frequency_hz;

    /* 触发信号频率更新事件 */
    if (handle->observer.on_signal_freq_update) {
        handle->observer.on_signal_freq_update(handle->signal_freq_result.frequency_hz, handle->observer_user_data);
    }

    return TIMESTAMP_HANDLER_OK;
}

/* 频率测量策略实现 */

/* 简单频率测量策略数据 */
typedef struct {
    bool first_capture;
    uint32_t timer_freq_hz;
    float min_freq;
    float max_freq;
} simple_freq_strategy_data_t;

/* 滤波频率测量策略数据 */
typedef struct {
    bool first_capture;
    uint32_t timer_freq_hz;
    float min_freq;
    float max_freq;
    float filter_alpha;
    float filtered_freq;
    uint32_t update_count;
    uint32_t update_period;
} filtered_freq_strategy_data_t;

/* 简单策略函数 */
static timestamp_handler_ret_t simple_freq_init(void *strategy_data, const timestamp_config_t *config)
{
    simple_freq_strategy_data_t *data = (simple_freq_strategy_data_t *)strategy_data;
    if (data == NULL || config == NULL) return TIMESTAMP_HANDLER_ERROR_PARAM;

    data->first_capture = true;
    data->timer_freq_hz = config->timer_freq_hz;
    data->min_freq = config->min_valid_freq_hz;
    data->max_freq = config->max_valid_freq_hz;
    return TIMESTAMP_HANDLER_OK;
}

static timestamp_handler_ret_t simple_freq_process(void *strategy_data, uint16_t period_ticks,
                                                  timestamp_freq_result_t *result)
{
    simple_freq_strategy_data_t *data = (simple_freq_strategy_data_t *)strategy_data;
    if (data == NULL || result == NULL) return TIMESTAMP_HANDLER_ERROR_PARAM;

    if (data->first_capture) {
        data->first_capture = false;
        result->valid = false;
        return TIMESTAMP_HANDLER_OK;
    }

    if (period_ticks == 0) {
        result->valid = false;
        return TIMESTAMP_HANDLER_OK;
    }

    float frequency = timestamp_utils_calc_frequency(period_ticks, data->timer_freq_hz);
    bool valid = timestamp_utils_is_frequency_valid(frequency, data->min_freq, data->max_freq);

    result->frequency_hz = frequency;
    result->period_us = valid ? (1000000.0f / frequency) : 0.0f;
    result->period_ticks = period_ticks;
    result->valid = valid;
    result->update_count++;

    return TIMESTAMP_HANDLER_OK;
}

static timestamp_handler_ret_t simple_freq_reset(void *strategy_data)
{
    simple_freq_strategy_data_t *data = (simple_freq_strategy_data_t *)strategy_data;
    if (data == NULL) return TIMESTAMP_HANDLER_ERROR_PARAM;

    data->first_capture = true;
    return TIMESTAMP_HANDLER_OK;
}

/* 滤波策略函数 */
static timestamp_handler_ret_t filtered_freq_init(void *strategy_data, const timestamp_config_t *config)
{
    filtered_freq_strategy_data_t *data = (filtered_freq_strategy_data_t *)strategy_data;
    if (data == NULL || config == NULL) return TIMESTAMP_HANDLER_ERROR_PARAM;

    data->first_capture = true;
    data->timer_freq_hz = config->timer_freq_hz;
    data->min_freq = config->min_valid_freq_hz;
    data->max_freq = config->max_valid_freq_hz;
    data->filter_alpha = config->freq_filter_alpha;
    data->filtered_freq = config->nominal_freq_hz;
    data->update_count = 0;
    data->update_period = config->freq_update_period;
    return TIMESTAMP_HANDLER_OK;
}

static timestamp_handler_ret_t filtered_freq_process(void *strategy_data, uint16_t period_ticks,
                                                    timestamp_freq_result_t *result)
{
    filtered_freq_strategy_data_t *data = (filtered_freq_strategy_data_t *)strategy_data;
    if (data == NULL || result == NULL) return TIMESTAMP_HANDLER_ERROR_PARAM;

    if (data->first_capture) {
        data->first_capture = false;
        result->valid = false;
        return TIMESTAMP_HANDLER_OK;
    }

    if (period_ticks == 0) {
        result->valid = false;
        return TIMESTAMP_HANDLER_OK;
    }

    float frequency = timestamp_utils_calc_frequency(period_ticks, data->timer_freq_hz);
    bool valid = timestamp_utils_is_frequency_valid(frequency, data->min_freq, data->max_freq);

    if (valid) {
        data->filtered_freq = timestamp_utils_filter_frequency(data->filtered_freq, frequency, data->filter_alpha);
        data->update_count++;

        /* 每N个周期更新一次结果 */
        if (data->update_count >= data->update_period) {
            result->frequency_hz = data->filtered_freq;
            result->period_us = 1000000.0f / data->filtered_freq;
            result->period_ticks = period_ticks;
            result->valid = true;
            result->update_count++;
            data->update_count = 0;
        } else {
            result->valid = false;
        }
    } else {
        result->valid = false;
    }

    return TIMESTAMP_HANDLER_OK;
}

/* 策略接口定义 */
static const timestamp_freq_strategy_interface_t simple_freq_strategy = {
    .name = "Simple",
    .init = simple_freq_init,
    .process = simple_freq_process,
    .reset = simple_freq_reset,
    .get_status = NULL
};

static const timestamp_freq_strategy_interface_t filtered_freq_strategy = {
    .name = "Filtered",
    .init = filtered_freq_init,
    .process = filtered_freq_process,
    .reset = simple_freq_reset,  /* 复用简单策略的重置 */
    .get_status = NULL
};

/* 策略获取函数 */
const timestamp_freq_strategy_interface_t* timestamp_handler_get_simple_freq_strategy(void)
{
    return &simple_freq_strategy;
}

const timestamp_freq_strategy_interface_t* timestamp_handler_get_filtered_freq_strategy(void)
{
    return &filtered_freq_strategy;
}

const timestamp_freq_strategy_interface_t* timestamp_handler_get_adaptive_freq_strategy(void)
{
    /* 自适应策略暂时返回滤波策略 */
    return &filtered_freq_strategy;
}
