/******************************************************************************
 * @file timestamp_driver.c
 * @brief 时间戳硬件驱动层实现
 * <AUTHOR> Assistant
 * @version 1.0
 * @date 2024-12-19
 *
 * @copyright Copyright (c) 2024
 *
 * Processing flow:
 *
 * 1.时间戳驱动实例化所需接口文件以及构造bsp_timestamp_driver_t驱动实例
 * 2.专注于底层硬件操作，提供定时器捕获功能
 * 3.支持TIM3和TIM4的输入捕获功能
 *
 * @par dependencies
 * timestamp_driver.h
 * elog.h
 * @note 1 tab == 4 spaces!
 *
 *****************************************************************************/

//******************************** Includes *********************************//
#include "timestamp_driver.h"

//******************************** Includes *********************************//

//******************************** Defines **********************************//
#define TIM_CHANNEL_1_INDEX     0   // TIM通道1索引
#define TIM_CHANNEL_2_INDEX     1   // TIM通道2索引
#define TIM_CHANNEL_3_INDEX     2   // TIM通道3索引
#define TIM_CHANNEL_4_INDEX     3   // TIM通道4索引

#define CAPTURE_COUNT_OVERFLOW_THRESHOLD    0xFFFC  // 捕获计数溢出阈值
//******************************** Defines **********************************//

//******************************** Variables ********************************//
//时间戳驱动对象实例链表，方便后续多个实例使用管理
static PNode gp_timestamp_private_data_list = NULL;

//时间戳驱动私有数据结构体
typedef struct timestamp_private_data_t
{
    // TIM接口数组
    tim_interface_t                      *ptim_interface;
    // 动态分配接口
    timestamp_dynamic_allocation_t       *pdynamic_allocation;
    // 捕获中断回调接口数组
    timestamp_capture_irq_callback_t     *pcapture_irq_callback;
    // 捕获完成通知回调接口
    pf_notify_capture_finish_fun_t       pfnotify_capture_finish;
    // 获取时间戳接口
    pf_get_timestamp_fun_t               pf_get_timestamp;
    // 驱动状态
    timestamp_driver_status_t            status;
    // 捕获计数器数组
    uint16_t                             capture_count[TIMESTAMP_TIM_QUANTITY];
#ifdef TIMESTAMP_RTOS_SUPPORTING
    // RTOS接口
    timestamp_rtos_interface_t           *prtos_interface;
#endif
}timestamp_private_data_t;

/* 全局驱动实例指针 */
static bsp_timestamp_driver_t *gp_global_timestamp_driver = NULL;

//******************************** Variables ********************************//

//******************************** Functions ********************************//
static int8_t timestamp_capture_irq_callback_fun(void *p_arg);
static timestamp_driver_ret_code_t timestamp_validate_input_data(timestamp_driver_input_data_t *pinput_data);
static timestamp_driver_ret_code_t timestamp_driver_init(bsp_timestamp_driver_t *pbsp_timestamp_driver);

/******************************************************************************
 * @brief 时间戳驱动初始化函数
 *
 * @param  pbsp_timestamp_driver 时间戳驱动实例
 *
 * @return timestamp_driver_ret_code_t
 * @retval TIMESTAMP_OK    初始化成功
 * @retval TIMESTAMP_ERROR 初始化失败
 *****************************************************************************/
static timestamp_driver_ret_code_t timestamp_driver_init(bsp_timestamp_driver_t *pbsp_timestamp_driver)
{
    if(NULL == pbsp_timestamp_driver)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("timestamp driver init error, driver instance is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERROR;
    }
    timestamp_private_data_t *psdata = (timestamp_private_data_t*)pbsp_timestamp_driver->private_data;
    if(NULL == psdata)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("timestamp driver init error, private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERROR;
    }

    //初始化TIM接口
    for(int i = 0; i < TIMESTAMP_TIM_QUANTITY; i++)
    {
        if(TIMESTAMP_OK != psdata->ptim_interface[i].pftim_init(psdata->ptim_interface[i].ptim_handler))
        {
            TIMESTAMP_DRIVER_LOG_ERROR("timestamp driver init error, tim init error tim port:%d, file:%s,line:%d", i, __FILE__,__LINE__);
            return TIMESTAMP_ERROR;
        }
    }

    //注册捕获中断回调函数
    for(int i = 0; i < TIMESTAMP_TIM_QUANTITY; i++)
    {
        (*psdata->pcapture_irq_callback[i].pf_capture_callback_fun) = timestamp_capture_irq_callback_fun;
        (*psdata->pcapture_irq_callback[i].p_arg) = pbsp_timestamp_driver;
    }

    //初始化状态
    psdata->status.initialized = true;
    psdata->status.tim_grid_enabled = false;
    psdata->status.tim_signal_enabled = false;
    psdata->status.grid_capture_count = 0;
    psdata->status.signal_capture_count = 0;
    psdata->status.overflow_count = 0;
    psdata->status.error_count = 0;
    psdata->status.last_grid_capture_value = 0;
    psdata->status.last_signal_capture_value = 0;

    //初始化捕获计数器
    for(int i = 0; i < TIMESTAMP_TIM_QUANTITY; i++)
    {
        psdata->capture_count[i] = 0;
    }

    TIMESTAMP_DRIVER_LOG_DEUBG("timestamp driver init success");
    return TIMESTAMP_OK;
}

/******************************************************************************
 * @brief 验证输入数据
 *
 * @param  pinput_data     输入数据
 *
 * @return timestamp_driver_ret_code_t
 *****************************************************************************/
static timestamp_driver_ret_code_t timestamp_validate_input_data(timestamp_driver_input_data_t *pinput_data)
{
    TIMESTAMP_DRIVER_LOG_DEUBG("timestamp_validate_input_data start");

    if(pinput_data == NULL)
    {
        return TIMESTAMP_ERRORPARAMETER;
    }

    // 检查TIM接口
    if(pinput_data->ptim_interface == NULL)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("timestamp_validate_input_data tim interface is NULL,file:%s,line:%d",
                                                                __FILE__, __LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    // 检查TIM接口函数指针
    for(int i = 0; i < TIMESTAMP_TIM_QUANTITY; i++)
    {
        if((NULL == pinput_data->ptim_interface[i].pftim_init) ||
           (NULL == pinput_data->ptim_interface[i].pftim_deinit) ||
           (NULL == pinput_data->ptim_interface[i].pftim_start_capture) ||
           (NULL == pinput_data->ptim_interface[i].pftim_stop_capture) ||
           (NULL == pinput_data->ptim_interface[i].pftim_get_counter) ||
           (NULL == pinput_data->ptim_interface[i].pftim_get_capture_value) ||
           (NULL == pinput_data->ptim_interface[i].pftim_set_counter))
        {
            TIMESTAMP_DRIVER_LOG_ERROR("timestamp_validate_input_data tim interface function pointer is NULL, tim:%d, file:%s,line:%d",
                                                                    i, __FILE__, __LINE__);
            return TIMESTAMP_ERRORPARAMETER;
        }
    }

    // 检查动态分配接口
    if((NULL == pinput_data->pdynamic_allocation) ||
       (NULL == pinput_data->pdynamic_allocation->pfmalloc) ||
       (NULL == pinput_data->pdynamic_allocation->pffree))
    {
        TIMESTAMP_DRIVER_LOG_ERROR("timestamp_validate_input_data dynamic allocation interface is NULL,file:%s,line:%d",
                                                                __FILE__, __LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    // 检查捕获中断回调接口
    if(pinput_data->pcapture_irq_callback == NULL)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("timestamp_validate_input_data capture irq callback is NULL,file:%s,line:%d",
                                                                __FILE__, __LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    TIMESTAMP_DRIVER_LOG_DEUBG("timestamp_validate_input_data end");
    return TIMESTAMP_OK;
}

timestamp_driver_ret_code_t bsp_timestamp_driver_instance(bsp_timestamp_driver_t *pbsp_timestamp_driver,
                                                         timestamp_driver_input_data_t *pinput_data)
{
    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_instance start");

    /* 0. 定义相关变量 */
    timestamp_driver_ret_code_t ret = TIMESTAMP_OK;
    timestamp_private_data_t *psdata = NULL;

    /* 1.检查输入对象是否有效 */
    if(pbsp_timestamp_driver == NULL)
    {
        return TIMESTAMP_ERRORPARAMETER;
    }

    /* 1.1 验证输入数据 */
    ret = timestamp_validate_input_data(pinput_data);
    if(TIMESTAMP_OK != ret)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_instance validate input data error,file:%s,line:%d",
                                                                __FILE__, __LINE__);
        return ret;
    }

    /* 2. 分配私有数据内存 */
    psdata = (timestamp_private_data_t*)pinput_data->pdynamic_allocation->pfmalloc(sizeof(timestamp_private_data_t));
    if(NULL == psdata)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_instance malloc private data error,file:%s,line:%d",
                                                                __FILE__, __LINE__);
        return TIMESTAMP_ERROR;
    }

    /* 3. 挂载接口，实例化driver句柄 */
    psdata->ptim_interface = pinput_data->ptim_interface;
    psdata->pdynamic_allocation = pinput_data->pdynamic_allocation;
    psdata->pcapture_irq_callback = pinput_data->pcapture_irq_callback;
    psdata->pfnotify_capture_finish = pinput_data->pfnotify_capture_finish;
    psdata->pf_get_timestamp = pinput_data->pf_get_timestamp;
#ifdef TIMESTAMP_RTOS_SUPPORTING
    psdata->prtos_interface = pinput_data->prtos_interface;
#endif

    pbsp_timestamp_driver->private_data = psdata;

    /* 4. 初始化驱动 */
    ret = timestamp_driver_init(pbsp_timestamp_driver);
    if(TIMESTAMP_OK != ret)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_instance init error,file:%s,line:%d", __FILE__, __LINE__);
        pinput_data->pdynamic_allocation->pffree(psdata);
        return ret;
    }

    /* 5. 添加到链表 */
    if(NULL == gp_timestamp_private_data_list)
    {
        gp_timestamp_private_data_list = create_list();
    }
    insert_node(gp_timestamp_private_data_list, psdata);

    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_instance end");
    return TIMESTAMP_OK;
}

timestamp_driver_ret_code_t bsp_timestamp_driver_start_capture(bsp_timestamp_driver_t *pbsp_timestamp_driver,
                                                              timestamp_tim_quantity_t tim_type,
                                                              uint32_t channel)
{
    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_start_capture start");

    if(NULL == pbsp_timestamp_driver)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_start_capture driver instance is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    timestamp_private_data_t *psdata = (timestamp_private_data_t*)pbsp_timestamp_driver->private_data;
    if(NULL == psdata)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_start_capture private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    if(!psdata->status.initialized)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_start_capture driver not initialized,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERROR;
    }

    if(tim_type >= TIMESTAMP_TIM_QUANTITY)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_start_capture invalid tim type:%d,file:%s,line:%d", tim_type, __FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    /* 启动定时器捕获 */
    timestamp_driver_ret_code_t ret = psdata->ptim_interface[tim_type].pftim_start_capture(
                                        psdata->ptim_interface[tim_type].ptim_handler, channel);
    if(TIMESTAMP_OK != ret)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_start_capture start capture error, tim:%d, channel:%d, file:%s,line:%d",
                                  tim_type, (int)channel, __FILE__,__LINE__);
        psdata->status.error_count++;
        return ret;
    }

    /* 更新状态 */
    if(tim_type == TIMESTAMP_TIM_GRID_ZERO)
    {
        psdata->status.tim_grid_enabled = true;
    }
    else if(tim_type == TIMESTAMP_TIM_SIGNAL_FREQ)
    {
        psdata->status.tim_signal_enabled = true;
    }

    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_start_capture end");
    return TIMESTAMP_OK;
}

timestamp_driver_ret_code_t bsp_timestamp_driver_stop_capture(bsp_timestamp_driver_t *pbsp_timestamp_driver,
                                                             timestamp_tim_quantity_t tim_type,
                                                             uint32_t channel)
{
    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_stop_capture start");

    if(NULL == pbsp_timestamp_driver)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_stop_capture driver instance is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    timestamp_private_data_t *psdata = (timestamp_private_data_t*)pbsp_timestamp_driver->private_data;
    if(NULL == psdata)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_stop_capture private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    if(!psdata->status.initialized)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_stop_capture driver not initialized,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERROR;
    }

    if(tim_type >= TIMESTAMP_TIM_QUANTITY)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_stop_capture invalid tim type:%d,file:%s,line:%d", tim_type, __FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    /* 停止定时器捕获 */
    timestamp_driver_ret_code_t ret = psdata->ptim_interface[tim_type].pftim_stop_capture(
                                        psdata->ptim_interface[tim_type].ptim_handler, channel);
    if(TIMESTAMP_OK != ret)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_stop_capture stop capture error, tim:%d, channel:%d, file:%s,line:%d",
                                  tim_type, (int)channel, __FILE__,__LINE__);
        psdata->status.error_count++;
        return ret;
    }

    /* 更新状态 */
    if(tim_type == TIMESTAMP_TIM_GRID_ZERO)
    {
        psdata->status.tim_grid_enabled = false;
    }
    else if(tim_type == TIMESTAMP_TIM_SIGNAL_FREQ)
    {
        psdata->status.tim_signal_enabled = false;
    }

    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_stop_capture end");
    return TIMESTAMP_OK;
}

timestamp_driver_ret_code_t bsp_timestamp_driver_get_counter(bsp_timestamp_driver_t *pbsp_timestamp_driver,
                                                            timestamp_tim_quantity_t tim_type,
                                                            uint16_t *count_value)
{
    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_get_counter start");

    if(NULL == pbsp_timestamp_driver || NULL == count_value)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_get_counter parameter is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    timestamp_private_data_t *psdata = (timestamp_private_data_t*)pbsp_timestamp_driver->private_data;
    if(NULL == psdata)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_get_counter private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    if(!psdata->status.initialized)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_get_counter driver not initialized,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERROR;
    }

    if(tim_type >= TIMESTAMP_TIM_QUANTITY)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_get_counter invalid tim type:%d,file:%s,line:%d", tim_type, __FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    /* 获取定时器计数值 */
    timestamp_driver_ret_code_t ret = psdata->ptim_interface[tim_type].pftim_get_counter(
                                        psdata->ptim_interface[tim_type].ptim_handler, count_value);
    if(TIMESTAMP_OK != ret)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_get_counter get counter error, tim:%d, file:%s,line:%d",
                                  tim_type, __FILE__,__LINE__);
        psdata->status.error_count++;
        return ret;
    }

    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_get_counter end, tim:%d, count:%u", tim_type, *count_value);
    return TIMESTAMP_OK;
}

timestamp_driver_ret_code_t bsp_timestamp_driver_set_counter(bsp_timestamp_driver_t *pbsp_timestamp_driver,
                                                            timestamp_tim_quantity_t tim_type,
                                                            uint16_t count_value)
{
    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_set_counter start");

    if(NULL == pbsp_timestamp_driver)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_set_counter parameter is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    timestamp_private_data_t *psdata = (timestamp_private_data_t*)pbsp_timestamp_driver->private_data;
    if(NULL == psdata)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_set_counter private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    if(!psdata->status.initialized)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_set_counter driver not initialized,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERROR;
    }

    if(tim_type >= TIMESTAMP_TIM_QUANTITY)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_set_counter invalid tim type:%d,file:%s,line:%d", tim_type, __FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    /* 设置定时器计数值 */
    timestamp_driver_ret_code_t ret = psdata->ptim_interface[tim_type].pftim_set_counter(
                                        psdata->ptim_interface[tim_type].ptim_handler, count_value);
    if(TIMESTAMP_OK != ret)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_set_counter set counter error, tim:%d, file:%s,line:%d",
                                  tim_type, __FILE__,__LINE__);
        psdata->status.error_count++;
        return ret;
    }

    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_set_counter end, tim:%d, count:%u", tim_type, count_value);
    return TIMESTAMP_OK;
}

timestamp_driver_ret_code_t bsp_timestamp_driver_get_capture_value(bsp_timestamp_driver_t *pbsp_timestamp_driver,
                                                                  timestamp_tim_quantity_t tim_type,
                                                                  uint32_t channel,
                                                                  uint16_t *capture_value)
{
    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_get_capture_value start");

    if(NULL == pbsp_timestamp_driver || NULL == capture_value)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_get_capture_value parameter is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    timestamp_private_data_t *psdata = (timestamp_private_data_t*)pbsp_timestamp_driver->private_data;
    if(NULL == psdata)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_get_capture_value private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    if(!psdata->status.initialized)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_get_capture_value driver not initialized,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERROR;
    }

    if(tim_type >= TIMESTAMP_TIM_QUANTITY)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_get_capture_value invalid tim type:%d,file:%s,line:%d", tim_type, __FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    /* 获取定时器捕获值 */
    timestamp_driver_ret_code_t ret = psdata->ptim_interface[tim_type].pftim_get_capture_value(
                                        psdata->ptim_interface[tim_type].ptim_handler, channel, capture_value);
    if(TIMESTAMP_OK != ret)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_get_capture_value get capture value error, tim:%d, channel:%d, file:%s,line:%d",
                                  tim_type, (int)channel, __FILE__,__LINE__);
        psdata->status.error_count++;
        return ret;
    }

    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_get_capture_value end, tim:%d, channel:%d, value:%u",
                              tim_type, (int)channel, *capture_value);
    return TIMESTAMP_OK;
}

timestamp_driver_ret_code_t bsp_timestamp_driver_get_status(bsp_timestamp_driver_t *pbsp_timestamp_driver,
                                                           timestamp_driver_status_t *status)
{
    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_get_status start");

    if(NULL == pbsp_timestamp_driver || NULL == status)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_get_status parameter is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    timestamp_private_data_t *psdata = (timestamp_private_data_t*)pbsp_timestamp_driver->private_data;
    if(NULL == psdata)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_get_status private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    /* 复制状态信息 */
    *status = psdata->status;

    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_get_status end");
    return TIMESTAMP_OK;
}

timestamp_driver_ret_code_t bsp_timestamp_driver_reset_statistics(bsp_timestamp_driver_t *pbsp_timestamp_driver)
{
    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_reset_statistics start");

    if(NULL == pbsp_timestamp_driver)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_reset_statistics parameter is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    timestamp_private_data_t *psdata = (timestamp_private_data_t*)pbsp_timestamp_driver->private_data;
    if(NULL == psdata)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_reset_statistics private data is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    if(!psdata->status.initialized)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_reset_statistics driver not initialized,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERROR;
    }

    /* 重置统计信息 */
    psdata->status.grid_capture_count = 0;
    psdata->status.signal_capture_count = 0;
    psdata->status.overflow_count = 0;
    psdata->status.error_count = 0;
    psdata->status.last_grid_capture_value = 0;
    psdata->status.last_signal_capture_value = 0;

    /* 重置捕获计数器 */
    for(int i = 0; i < TIMESTAMP_TIM_QUANTITY; i++)
    {
        psdata->capture_count[i] = 0;
    }

    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_reset_statistics end");
    return TIMESTAMP_OK;
}

void bsp_timestamp_driver_capture_irq_handler(bsp_timestamp_driver_t *pbsp_timestamp_driver,
                                             timestamp_tim_quantity_t tim_type,
                                             uint32_t channel,
                                             uint16_t capture_value,
                                             tim_interface_capture_edge_t edge_type)
{
    TIMESTAMP_DRIVER_LOG_IRQ("bsp_timestamp_driver_capture_irq_handler tim:%d, ch:%d, val:%u",
                            tim_type, (int)channel, capture_value);

    if(NULL == pbsp_timestamp_driver)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_capture_irq_handler driver instance is NULL");
        return;
    }

    timestamp_private_data_t *psdata = (timestamp_private_data_t*)pbsp_timestamp_driver->private_data;
    if(NULL == psdata)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_capture_irq_handler private data is NULL");
        return;
    }

    if(!psdata->status.initialized)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_capture_irq_handler driver not initialized");
        return;
    }

    if(tim_type >= TIMESTAMP_TIM_QUANTITY)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_capture_irq_handler invalid tim type:%d", tim_type);
        psdata->status.error_count++;
        return;
    }

    /* 更新捕获计数 */
    psdata->capture_count[tim_type]++;

    /* 检查溢出 */
    if(psdata->capture_count[tim_type] >= CAPTURE_COUNT_OVERFLOW_THRESHOLD)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_capture_irq_handler capture count overflow, tim:%d, count:%u",
                                  tim_type, psdata->capture_count[tim_type]);
        psdata->status.overflow_count++;
        psdata->capture_count[tim_type] = 0;  // 重置计数器
    }

    /* 更新状态 */
    if(tim_type == TIMESTAMP_TIM_GRID_ZERO)
    {
        psdata->status.grid_capture_count++;
        psdata->status.last_grid_capture_value = capture_value;
    }
    else if(tim_type == TIMESTAMP_TIM_SIGNAL_FREQ)
    {
        psdata->status.signal_capture_count++;
        psdata->status.last_signal_capture_value = capture_value;
    }

    /* 调用捕获完成通知回调 */
    if(NULL != psdata->pfnotify_capture_finish)
    {
        psdata->pfnotify_capture_finish(pbsp_timestamp_driver);
    }
}

/******************************************************************************
 * @brief 捕获中断回调函数
 *
 * @param  p_arg    参数（驱动实例指针）
 *
 * @return int8_t   返回值
 *****************************************************************************/
static int8_t timestamp_capture_irq_callback_fun(void *p_arg)
{
    TIMESTAMP_DRIVER_LOG_IRQ("timestamp_capture_irq_callback_fun");

    bsp_timestamp_driver_t *pbsp_timestamp_driver = (bsp_timestamp_driver_t*)p_arg;
    if(NULL == pbsp_timestamp_driver)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("timestamp_capture_irq_callback_fun driver instance is NULL");
        return -1;
    }

    /* 这里可以添加额外的中断处理逻辑 */
    /* 具体的捕获处理由 bsp_timestamp_driver_capture_irq_handler 完成 */

    return 0;
}

/* 全局访问接口 */

bsp_timestamp_driver_t* bsp_timestamp_driver_get_global_instance(void)
{
    return gp_global_timestamp_driver;
}

timestamp_driver_ret_code_t bsp_timestamp_driver_set_global_instance(bsp_timestamp_driver_t *pbsp_timestamp_driver)
{
    if(NULL == pbsp_timestamp_driver)
    {
        TIMESTAMP_DRIVER_LOG_ERROR("bsp_timestamp_driver_set_global_instance parameter is NULL,file:%s,line:%d",__FILE__,__LINE__);
        return TIMESTAMP_ERRORPARAMETER;
    }

    gp_global_timestamp_driver = pbsp_timestamp_driver;
    TIMESTAMP_DRIVER_LOG_DEUBG("bsp_timestamp_driver_set_global_instance success");
    return TIMESTAMP_OK;
}

//******************************** Functions ********************************//
