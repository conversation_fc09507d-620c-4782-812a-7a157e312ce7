/**
 * @file zero_detect_example.c
 * @brief ZeroDetectTask 使用示例
 * <AUTHOR> Assistant
 * @version 1.0
 * @date 2024-12-19
 * 
 * 本文件展示了如何使用 ZeroDetectTask 进行零点检测和频率分析
 * 
 * 使用方法：
 * 1. 在 main 函数中调用 system_source_inst() 即可自动初始化和启动 ZeroDetectTask
 * 2. 或者手动调用集成接口进行初始化
 * 3. 通过全局接口获取零点检测信息
 */

#include "zero_detect_integration.h"
#include "zero_detect_task.h"
#include <stdio.h>

/* 示例：手动初始化和启动 ZeroDetectTask */
void zero_detect_manual_init_example(void)
{
    printf("=== ZeroDetectTask Manual Init Example ===\r\n");
    
    /* 1. 使用默认配置初始化并启动 */
    zero_detect_ret_t ret = zero_detect_integration_init(NULL, NULL);
    if (ret != ZERO_DETECT_OK) {
        printf("Failed to initialize zero detect integration: %d\r\n", ret);
        return;
    }
    
    ret = zero_detect_integration_start();
    if (ret != ZERO_DETECT_OK) {
        printf("Failed to start zero detect integration: %d\r\n", ret);
        return;
    }
    
    printf("ZeroDetectTask initialized and started successfully\r\n");
}

/* 示例：使用便捷宏初始化 */
void zero_detect_macro_init_example(void)
{
    printf("=== ZeroDetectTask Macro Init Example ===\r\n");
    
    /* 使用便捷宏初始化并启动 */
    ZERO_DETECT_INTEGRATION_INIT_AND_START();
    
    printf("ZeroDetectTask initialized using macro\r\n");
}

/* 示例：获取零点检测状态 */
void zero_detect_status_example(void)
{
    printf("=== ZeroDetectTask Status Example ===\r\n");
    
    zero_detect_status_t status;
    zero_detect_ret_t ret = zero_detect_get_global_status(&status);
    
    if (ret == ZERO_DETECT_OK) {
        printf("Zero Detect Status:\r\n");
        printf("  Grid Frequency: %.2f Hz (Valid: %s)\r\n", 
               status.grid_freq_hz, status.freq_valid ? "Yes" : "No");
        printf("  Signal Frequency: %.2f Hz\r\n", status.signal_freq_hz);
        printf("  Capture Count: %u\r\n", status.capture_count);
        printf("  T Zero: %u\r\n", status.t_zero);
        printf("  Total Zero Cross: %u\r\n", (unsigned int)status.total_zero_cross_count);
        printf("  Freq Updates: %u\r\n", (unsigned int)status.freq_update_count);
        printf("  Overflow Count: %u\r\n", (unsigned int)status.overflow_count);
    } else {
        printf("Failed to get zero detect status: %d\r\n", ret);
    }
}

/* 示例：获取频率信息 */
void zero_detect_frequency_example(void)
{
    printf("=== ZeroDetectTask Frequency Example ===\r\n");
    
    /* 使用便捷宏获取频率 */
    float grid_freq = ZERO_DETECT_GET_GRID_FREQ();
    float signal_freq = ZERO_DETECT_GET_SIGNAL_FREQ();
    
    printf("Current Grid Frequency: %.2f Hz\r\n", grid_freq);
    printf("Current Signal Frequency: %.2f Hz\r\n", signal_freq);
    
    /* 或者使用函数接口 */
    grid_freq = zero_detect_get_grid_frequency();
    signal_freq = zero_detect_get_signal_frequency();
    
    printf("Grid Frequency (function): %.2f Hz\r\n", grid_freq);
    printf("Signal Frequency (function): %.2f Hz\r\n", signal_freq);
}

/* 示例：获取时间戳信息 */
void zero_detect_timestamp_example(void)
{
    printf("=== ZeroDetectTask Timestamp Example ===\r\n");
    
    uint16_t capture_count, t_zero;
    zero_detect_ret_t ret = zero_detect_get_latest_zero_timestamp(&capture_count, &t_zero);
    
    if (ret == ZERO_DETECT_OK) {
        printf("Latest Zero Timestamp:\r\n");
        printf("  Capture Count: %u\r\n", capture_count);
        printf("  T Zero: %u\r\n", t_zero);
        
        /* 生成压缩时间戳 */
        uint32_t packed_timestamp = ZERO_DETECT_GENERATE_TIMESTAMP();
        printf("  Packed Timestamp: 0x%08X\r\n", (unsigned int)packed_timestamp);
        
        /* 解析压缩时间戳 */
        uint16_t packed_count = (packed_timestamp >> 16) & 0xFFFF;
        uint16_t packed_t_sample0 = packed_timestamp & 0xFFFF;
        printf("  Packed Count: %u, T Sample0: %u\r\n", packed_count, packed_t_sample0);
    } else {
        printf("Failed to get zero timestamp: %d\r\n", ret);
    }
}

/* 示例：相位修正计算 */
void zero_detect_phase_correction_example(void)
{
    printf("=== ZeroDetectTask Phase Correction Example ===\r\n");
    
    /* 生成压缩时间戳 */
    uint32_t packed_timestamp = zero_detect_integration_generate_packed_timestamp();
    if (packed_timestamp == 0) {
        printf("Failed to generate packed timestamp\r\n");
        return;
    }
    
    /* 计算相位修正参数 */
    timestamp_phase_correction_t correction;
    zero_detect_ret_t ret = zero_detect_integration_calculate_phase_correction(packed_timestamp, &correction);
    
    if (ret == ZERO_DETECT_OK) {
        printf("Phase Correction Parameters:\r\n");
        printf("  Packed Timestamp: 0x%08X\r\n", (unsigned int)packed_timestamp);
        printf("  Phase Shift: %.6f radians\r\n", correction.phase_shift_rad);
        printf("  Frequency: %.2f Hz\r\n", correction.frequency_hz);
        printf("  Valid: %s\r\n", correction.valid ? "Yes" : "No");
    } else {
        printf("Failed to calculate phase correction: %d\r\n", ret);
    }
}

/* 示例：打印集成状态 */
void zero_detect_print_status_example(void)
{
    printf("=== ZeroDetectTask Print Status Example ===\r\n");
    
    /* 打印集成状态 */
    zero_detect_integration_print_status();
}

/* 示例：自定义回调函数 */
void zero_cross_callback(uint16_t capture_count, uint16_t t_zero, float frequency, void *user_data)
{
    printf("[Callback] Zero Cross: count=%u, t_zero=%u, freq=%.2f Hz\r\n", 
           capture_count, t_zero, frequency);
}

void signal_freq_callback(float frequency, void *user_data)
{
    printf("[Callback] Signal Freq: %.2f Hz\r\n", frequency);
}

void overflow_callback(uint16_t old_capture_count, void *user_data)
{
    printf("[Callback] Overflow: old_count=%u\r\n", old_capture_count);
}

void error_callback(zero_detect_ret_t error_code, const char *error_msg, void *user_data)
{
    printf("[Callback] Error: code=%d, msg=%s\r\n", error_code, error_msg ? error_msg : "NULL");
}

/* 示例：注册回调函数 */
void zero_detect_callback_example(void)
{
    printf("=== ZeroDetectTask Callback Example ===\r\n");
    
    /* 注意：这个示例需要在手动初始化时使用，不能与集成接口同时使用 */
    zero_detect_handle_t handle;
    zero_detect_config_t config = zero_detect_get_default_config();
    
    /* 初始化任务 */
    zero_detect_ret_t ret = zero_detect_task_init(&handle, &config);
    if (ret != ZERO_DETECT_OK) {
        printf("Failed to init zero detect task: %d\r\n", ret);
        return;
    }
    
    /* 注册回调函数 */
    zero_detect_callbacks_t callbacks = {
        .on_zero_cross = zero_cross_callback,
        .on_signal_freq_update = signal_freq_callback,
        .on_overflow = overflow_callback,
        .on_error = error_callback,
        .user_data = NULL
    };
    
    ret = zero_detect_task_register_callbacks(&handle, &callbacks);
    if (ret != ZERO_DETECT_OK) {
        printf("Failed to register callbacks: %d\r\n", ret);
        return;
    }
    
    /* 启动任务 */
    ret = zero_detect_task_start(&handle);
    if (ret != ZERO_DETECT_OK) {
        printf("Failed to start zero detect task: %d\r\n", ret);
        return;
    }
    
    printf("ZeroDetectTask with callbacks started successfully\r\n");
    
    /* 注意：实际使用中需要在适当的时候停止和反初始化任务 */
}

/* 示例：完整的使用流程 */
void zero_detect_complete_example(void)
{
    printf("=== ZeroDetectTask Complete Example ===\r\n");
    
    /* 1. 初始化和启动 */
    zero_detect_manual_init_example();
    
    /* 2. 等待一段时间让任务运行 */
    printf("Waiting for task to run...\r\n");
    // 在实际应用中，这里应该是 vTaskDelay() 或其他延时
    
    /* 3. 获取状态信息 */
    zero_detect_status_example();
    
    /* 4. 获取频率信息 */
    zero_detect_frequency_example();
    
    /* 5. 获取时间戳信息 */
    zero_detect_timestamp_example();
    
    /* 6. 相位修正示例 */
    zero_detect_phase_correction_example();
    
    /* 7. 打印状态 */
    zero_detect_print_status_example();
    
    /* 8. 停止和反初始化 */
    ZERO_DETECT_INTEGRATION_STOP_AND_DEINIT();
    
    printf("ZeroDetectTask complete example finished\r\n");
}

/* 主示例函数 */
void zero_detect_examples_run(void)
{
    printf("=== ZeroDetectTask Examples ===\r\n");
    
    /* 运行完整示例 */
    zero_detect_complete_example();
    
    printf("=== All Examples Completed ===\r\n");
}
