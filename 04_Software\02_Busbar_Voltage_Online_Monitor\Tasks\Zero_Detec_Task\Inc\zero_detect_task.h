/******************************************************************************
 * @file zero_detect_task.h
 * @brief 零点检测任务头文件
 * <AUTHOR> Assistant
 * @version 1.0
 * @date 2024-12-19
 *
 * @copyright Copyright (c) 2024
 *
 * 功能说明：
 * 1. TIM4专用零点捕获：捕获市电过零点上升沿，更新t_zero和capture_count
 * 2. TIM3专用信号频率检测：独立检测采样信号频率signal_freq_hz
 * 3. 频率计算：每5个周期更新一次grid_freq_hz（45-65Hz有效范围）
 * 4. 溢出处理：capture_count >= 0xFFFC时触发重置流程
 * 5. 观察者模式：支持订阅机制，允许其他模块注册回调函数
 *
 * @note 1 tab == 4 spaces!
 *
 *****************************************************************************/

#ifndef __ZERO_DETECT_TASK_H__
#define __ZERO_DETECT_TASK_H__

#ifdef __cplusplus
extern "C" {
#endif

/* ==================== 头文件包含 ==================== */
#include <stdint.h>
#include <stdbool.h>
#include "task.h"
#include "semphr.h"

/* ==================== 宏定义 ==================== */
/* 零点检测任务配置 */
#define ZERO_DETECT_TASK_NAME                   "ZeroDetectTask"
#define ZERO_DETECT_TASK_STACK_SIZE             512U        /* 任务栈大小 */
#define ZERO_DETECT_TASK_PRIORITY               6U          /* 任务优先级 */

/* 频率更新配置 - 按文档要求每5个周期更新一次 */
#define ZERO_DETECT_FREQ_UPDATE_CYCLES          5U          /* 频率更新周期数 */
#define ZERO_DETECT_MIN_VALID_FREQ_HZ           45.0f       /* 最小有效频率 */
#define ZERO_DETECT_MAX_VALID_FREQ_HZ           65.0f       /* 最大有效频率 */
#define ZERO_DETECT_NOMINAL_FREQ_HZ             50.0f       /* 标称频率 */

/* 溢出控制 - 按文档要求接近65535时触发重置 */
#define ZERO_DETECT_CAPTURE_OVERFLOW_THRESHOLD  0xFFFC      /* 溢出阈值 */

/* 压缩时间戳宏定义 */
#define PACK_TIMESTAMP(capture, cnt)            (((capture) << 16) | ((cnt) & 0xFFFF))
#define UNPACK_CAPTURE(ts)                      ((ts) >> 16)
#define UNPACK_T0(ts)                           ((ts) & 0xFFFF)

/* 观察者模式配置 */
#define ZERO_DETECT_MAX_CALLBACKS               8           /* 最大回调函数数量 */

/* 调试开关 */
#define ZERO_DETECT_DEBUG_ENABLE                1

#if ZERO_DETECT_DEBUG_ENABLE
#define ZERO_DETECT_LOG_DEBUG(fmt, ...)         printf("[ZD_DEBUG] " fmt "\r\n", ##__VA_ARGS__)
#define ZERO_DETECT_LOG_INFO(fmt, ...)          printf("[ZD_INFO] " fmt "\r\n", ##__VA_ARGS__)
#define ZERO_DETECT_LOG_ERROR(fmt, ...)         printf("[ZD_ERROR] " fmt "\r\n", ##__VA_ARGS__)
#define ZERO_DETECT_LOG_WARNING(fmt, ...)       printf("[ZD_WARNING] " fmt "\r\n", ##__VA_ARGS__)
#else
#define ZERO_DETECT_LOG_DEBUG(fmt, ...)
#define ZERO_DETECT_LOG_INFO(fmt, ...)
#define ZERO_DETECT_LOG_ERROR(fmt, ...)
#define ZERO_DETECT_LOG_WARNING(fmt, ...)
#endif

/* ==================== 类型定义 ==================== */
/* 返回值类型 */
typedef enum {
    ZERO_DETECT_OK = 0,                         /* 成功 */
    ZERO_DETECT_ERROR = -1,                     /* 通用错误 */
    ZERO_DETECT_ERROR_PARAM = -2,               /* 参数错误 */
    ZERO_DETECT_ERROR_NOT_INIT = -3,            /* 未初始化 */
    ZERO_DETECT_ERROR_ALREADY_INIT = -4,        /* 已初始化 */
    ZERO_DETECT_ERROR_OVERFLOW = -5,            /* 溢出错误 */
    ZERO_DETECT_ERROR_TIMEOUT = -6,             /* 超时错误 */
    ZERO_DETECT_ERROR_MEMORY = -7,              /* 内存错误 */
} zero_detect_ret_t;

/* 电网同步状态结构体 - 按文档规范定义 */
typedef struct {
    uint16_t capture_count;                     /* 过零点捕获计数 */
    uint16_t t_zero;                           /* 最近一次过零点时间戳(16位CNT值) */
    float grid_freq_hz;                        /* 电网频率估算值 */
    float signal_freq_hz;                      /* 采样信号频率估算值 */
    bool freq_valid;                           /* 频率有效标志 */
    uint32_t total_zero_cross_count;           /* 总零点捕获次数 */
    uint32_t freq_update_count;                /* 频率更新次数 */
    uint32_t overflow_count;                   /* 溢出次数 */
} zero_detect_status_t;

/* 零点检测配置结构体 */
typedef struct {
    uint16_t task_stack_size;                  /* 任务栈大小 */
    uint8_t task_priority;                     /* 任务优先级 */
    uint16_t freq_update_cycles;               /* 频率更新周期数 */
    float min_valid_freq_hz;                   /* 最小有效频率 */
    float max_valid_freq_hz;                   /* 最大有效频率 */
    uint16_t capture_overflow_threshold;       /* 捕获溢出阈值 */
} zero_detect_config_t;

/* 观察者回调函数类型定义 */
typedef void (*zero_detect_zero_cross_callback_t)(uint16_t capture_count, uint16_t t_zero, 
                                                  float frequency, void *user_data);
typedef void (*zero_detect_signal_freq_callback_t)(float frequency, void *user_data);
typedef void (*zero_detect_overflow_callback_t)(uint16_t old_capture_count, void *user_data);
typedef void (*zero_detect_error_callback_t)(zero_detect_ret_t error_code, 
                                             const char *error_msg, void *user_data);

/* 观察者回调结构体 - 保留用于新架构 */
typedef struct {
    zero_detect_zero_cross_callback_t on_zero_cross;        /* 零点捕获回调 */
    zero_detect_signal_freq_callback_t on_signal_freq;      /* 信号频率回调 */
    zero_detect_overflow_callback_t on_overflow;            /* 溢出回调 */
    zero_detect_error_callback_t on_error;                  /* 错误回调 */
    void *user_data;                                        /* 用户数据 */
} zero_detect_callback_t;

/* ==================== RTOS接口类型定义 ==================== */

/* RTOS操作返回值类型 */
typedef enum 
{
    ZERO_DETECT_RTOS_PDFALSE = 0,       /* 返回值为假 */
    ZERO_DETECT_RTOS_PDTRUE  = 1,       /* 返回值为真 */
}zero_detect_handler_os_ret_code_t;

/* 任务函数类型定义 */
typedef void (*task_function_t)(void *);

/*                 From OS层：       RTOS 接口                                */
// 定义一个结构体，用于表示RTOS的接口
typedef struct zero_detect_handler_os_interface_t
{
    // 创建任务
    zero_detect_handler_os_ret_code_t (*rtos_task_create)               (task_function_t task_function,
                                                                         const char * const task_name,
                                                                         const uint16_t stack_size,
                                                                         void * const task_argument,
                                                                         uint32_t priority,
                                                                         void ** const task_handle);
    // 删除任务
    zero_detect_handler_os_ret_code_t (*rtos_task_delete)               (void * const task_handle);
    
    // 创建互斥量
    zero_detect_handler_os_ret_code_t (*rtos_mutex_create)              (void ** const pmutex);
    // 删除互斥量   
    zero_detect_handler_os_ret_code_t (*rtos_mutex_delete)              (void * const pmutex);
    // 获取互斥量   
    zero_detect_handler_os_ret_code_t (*rtos_mutex_take)                (void * const pmutex,
                                                                         uint32_t timeout);
    // 释放互斥量   
    zero_detect_handler_os_ret_code_t (*rtos_mutex_give)                (void * const pmutex);
    
    // 创建二进制信号量 
    zero_detect_handler_os_ret_code_t (*rtos_semphorebinary_create)     (void ** const psemphore);
    // 创建计数信号量
    zero_detect_handler_os_ret_code_t (*rtos_semphore_create)           (void ** const psemphore,
                                                                         uint32_t max_count,
                                                                         uint32_t initial_count);
    // 删除信号量   
    zero_detect_handler_os_ret_code_t (*rtos_semphore_delete)           (void * const psemphore);
    // 获取信号量   
    zero_detect_handler_os_ret_code_t (*rtos_semphore_take)             (void * const psemphore,
                                                                         uint32_t timeout);
    // 释放信号量   
    zero_detect_handler_os_ret_code_t (*rtos_semphore_give)             (void * const psemphore);
    // 中断中释放信号量 
    zero_detect_handler_os_ret_code_t (*rtos_semphore_give_formisr)     (void *const psemphore);
    
    // 任务通知，实现信号量    
    zero_detect_handler_os_ret_code_t (*rtos_task_notifiy_give_fromisr) (void * const task_handle);
    // 任务等待，实现信号量    
    zero_detect_handler_os_ret_code_t (*rtos_task_notifiy_take)         (uint32_t timeout);
    // 清空任务通知    
    zero_detect_handler_os_ret_code_t (*rtos_task_notifiy_clear)        (void * const task_handle);
    // 发送任务通知
    zero_detect_handler_os_ret_code_t (*rtos_task_notify)               (void * const task_handle,
                                                                         uint32_t value,
                                                                         uint32_t action);
    // 等待任务通知
    zero_detect_handler_os_ret_code_t (*rtos_task_notify_wait)          (uint32_t bits_to_clear_on_entry,
                                                                         uint32_t bits_to_clear_on_exit,
                                                                         uint32_t *notification_value,
                                                                         uint32_t timeout);
    // 获取任务句柄 
    zero_detect_handler_os_ret_code_t (*rtos_task_get_handle)           (void **const task_handle);
    // 获取当前任务句柄
    zero_detect_handler_os_ret_code_t (*rtos_task_get_current_handle)   (void **const task_handle);
    
    // 临界区进入
    zero_detect_handler_os_ret_code_t (*rtos_enter_critical)            (void);
    // 临界区退出
    zero_detect_handler_os_ret_code_t (*rtos_exit_critical)             (void);
    // 中断中临界区进入
    zero_detect_handler_os_ret_code_t (*rtos_enter_critical_fromisr)    (void);
    // 中断中临界区退出
    zero_detect_handler_os_ret_code_t (*rtos_exit_critical_fromisr)     (uint32_t saved_interrupt_status);
    
    // 延时函数
    zero_detect_handler_os_ret_code_t (*rtos_delay)                     (uint32_t delay_ms);
    // 获取系统时钟节拍
    uint32_t                          (*rtos_get_tick_count)            (void);
    // 毫秒转换为节拍
    uint32_t                          (*rtos_ms_to_ticks)               (uint32_t ms);
}zero_detect_handler_os_interface_t;
/*                 From OS层：       RTOS 接口                                */

/* ==================== 系统配置接口 ==================== */

/*                 From Core层：     外部的栈空间分配                           */
/* 由外部决定分配的栈空间大小(系统集成层决定)
*  zero_detect_task线程在创建的时候已确定，无需在进行分配
*/
typedef struct
{
    uint8_t  zero_detect_task_priority;                 /* 零点检测任务优先级 */
    uint16_t zero_detect_task_stack_size;               /* 零点检测任务栈大小 */
    uint16_t freq_update_cycles;                        /* 频率更新周期数 */
    float    min_valid_freq_hz;                         /* 最小有效频率 */
    float    max_valid_freq_hz;                         /* 最大有效频率 */
    uint16_t capture_overflow_threshold;                /* 捕获溢出阈值 */
}zero_detect_system_config_t;

/*                 From Core层：     时基接口                                 */
typedef struct
{
    uint32_t (*pfget_timetick_ms) (void);               /* 获取毫秒时间戳 */
    uint32_t (*pfget_timetick_us) (void);               /* 获取微秒时间戳 */
}zero_detect_system_timebase_interface_t;
/*                 From Core层：     时基接口                                 */

/*                 From Core层：     中断 接口                                */
typedef struct
{
    // 初始化函数指针
    int8_t (*pfinit)                     (void);
    // 反初始化函数指针
    int8_t (*pfdeinit)                   (void);
    // 使能中断函数指针
    int8_t (*pfenable_irq)               (void);
    // 禁用中断函数指针
    int8_t (*pfdisable_irq)              (void);
    // 使能外设时钟函数指针
    int8_t (*pfenable_peripheral_clock)  (void);
    // 禁用外设时钟函数指针
    int8_t (*pfdisable_peripheral_clock) (void);
}zero_detect_system_interrupt_interface_t;
/*                 From Core层：     中断 接口                                */

/*                 From Core层：     中断回调函数接口                          */
typedef struct 
{
    int8_t (**tim4_capture_irq_callbackfun)(void*);     /* TIM4捕获中断回调函数指针 */
    int8_t (**tim3_capture_irq_callbackfun)(void*);     /* TIM3捕获中断回调函数指针 */
    void   const **p_params;                            /* 回调函数参数指针 */
}zero_detect_callback_fun_t;
/*                 From Core层：     中断回调函数接口                          */

/*                 From Core层：     定时器控制接口                            */
typedef struct 
{
    zero_detect_ret_t (*pfstart_tim4_capture)(void);    /* 开始TIM4零点捕获 */
    zero_detect_ret_t (*pfstop_tim4_capture) (void);    /* 停止TIM4零点捕获 */
    zero_detect_ret_t (*pfstart_tim3_capture)(void);    /* 开始TIM3信号频率捕获 */
    zero_detect_ret_t (*pfstop_tim3_capture) (void);    /* 停止TIM3信号频率捕获 */
    zero_detect_ret_t (*pfreset_capture_count)(void);   /* 重置捕获计数器 */
}zero_detect_timer_control_t;
/*                 From Core层：     定时器控制接口                            */

/*                 From Core层：     输入参数接口                              */
// 定义一个结构体，用于存储零点检测处理器的所有输入参数
typedef struct
{
    zero_detect_system_config_t                *p_system_config;
    // 系统时基接口指针
    zero_detect_system_timebase_interface_t    *p_system_timebase_interface;
    // 系统中断接口指针
    zero_detect_system_interrupt_interface_t   *p_system_interrupt_interface;
    // 零点检测回调函数指针
    zero_detect_callback_fun_t                 *p_zero_detect_callback_fun;
    // 零点检测定时器控制接口指针
    zero_detect_timer_control_t                *p_zero_detect_timer_control;
    // 零点检测处理器OS接口指针
    zero_detect_handler_os_interface_t         *p_zero_detect_handler_os_interface;
}zero_detect_handler_input_all_arg_t;
/*                 From Core层：     输入参数接口                              */

/* ==================== 句柄结构体重新定义 ==================== */

/*                 zero_detect_handler句柄                                    */
typedef struct zero_detect_handler_private_data_t zero_detect_handler_private_data_t;
typedef struct zero_detect_handler_t
{
    // 系统时基接口
    zero_detect_system_timebase_interface_t    *p_system_timebase_interface;
    // 系统中断接口
    zero_detect_system_interrupt_interface_t   *p_system_interrupt_interface;
    // 零点检测回调函数接口
    zero_detect_callback_fun_t                 *p_zero_detect_callback_fun;
    // 零点检测定时器控制接口
    zero_detect_timer_control_t                *p_zero_detect_timer_control;
    // RTOS接口
    zero_detect_handler_os_interface_t         *p_zero_detect_handler_os_interface;
    // 系统配置信息
    zero_detect_system_config_t                *p_system_config;
    // 零点检测任务线程
    void                                       *zero_detect_task_thread;
    // 零点检测任务互斥锁
    void                                       *zero_detect_mutex;
    // 零点检测任务信号量
    void                                       *zero_detect_semphore;
    // 私有数据指针
    zero_detect_handler_private_data_t         *p_private_data;
}zero_detect_handler_t;

/* ==================== 函数声明更新 ==================== */

/******************************************************************************
 * @brief 零点检测处理器实例化
 *
 * @param  p_handler               零点检测句柄指针
 * @param  p_input_data            输入参数结构体指针
 *
 * @return zero_detect_ret_t       返回值
 *****************************************************************************/
zero_detect_ret_t zero_detect_handler_inst(zero_detect_handler_t *p_handler,
                                          zero_detect_handler_input_all_arg_t *p_input_data);

/******************************************************************************
 * @brief 零点检测处理器反实例化
 *
 * @param  p_handler               零点检测句柄指针
 *
 * @return zero_detect_ret_t       返回值
 *****************************************************************************/
zero_detect_ret_t zero_detect_handler_deinst(zero_detect_handler_t *p_handler);

/******************************************************************************
 * @brief 零点检测处理器启动
 *
 * @param  p_handler               零点检测句柄指针
 *
 * @return zero_detect_ret_t       返回值
 *****************************************************************************/
zero_detect_ret_t zero_detect_handler_start(zero_detect_handler_t *p_handler);

/******************************************************************************
 * @brief 零点检测处理器停止
 *
 * @param  p_handler               零点检测句柄指针
 *
 * @return zero_detect_ret_t       返回值
 *****************************************************************************/
zero_detect_ret_t zero_detect_handler_stop(zero_detect_handler_t *p_handler);

/******************************************************************************
 * @brief 零点检测处理器线程
 *
 * @param  arg                     线程参数
 *
 * @return void
 *****************************************************************************/
void zero_detect_handler_thread(void *arg);

/* ==================== 对外接口函数声明 ==================== */

/* 观察者模式接口 - 外部模块注册回调函数 */
zero_detect_ret_t zero_detect_register_callback(const zero_detect_callback_t *callback);
zero_detect_ret_t zero_detect_unregister_callback(const zero_detect_callback_t *callback);

/* 线程启动接口 */
void zero_detect_handler_thread(void *argument);

/* 工具函数 */
zero_detect_system_config_t zero_detect_get_default_system_config(void);
const char* zero_detect_get_error_string(zero_detect_ret_t error);

#ifdef __cplusplus
}
#endif

#endif /* __ZERO_DETECT_TASK_H__ */
